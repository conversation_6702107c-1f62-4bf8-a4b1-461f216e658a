export interface User {
  id: number
  username: string
  email: string
  avatar?: string
  points: number
  level: number
  createdAt: string
}

export interface Document {
  id: number
  title: string
  description: string
  fileUrl: string
  fileType: string
  fileSize: number
  thumbnailUrl: string
  category: string
  tags: string[]
  uploader: User
  downloads: number
  likes: number
  points: number
  createdAt: string
}

export interface Comment {
  id: number
  content: string
  user: User
  createdAt: string
}

export interface Category {
  id: number
  name: string
  icon: any
  description: string
  bgColor?: string
  count?: number
}

export interface Tag {
  id: number
  name: string
  color: string
}

export interface DownloadRecord {
  id: number
  document: {
    id: number
    title: string
    thumbnailUrl: string
  }
  downloadedAt: string
  points: number
} 