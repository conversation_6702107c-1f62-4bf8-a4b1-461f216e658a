<template>
  <div class="page-container">
    <div class="container">
      <!-- Header -->
      <div class="header">
        <h1 class="title">资料库</h1>
        <p class="subtitle">查找、筛选并下载你需要的学习资料</p>
        
        <!-- 顶部搜索栏 -->
        <div class="top-search-bar">
          <div class="search-input-container" style="position:relative;">
            <input
              v-model="search"
              ref="searchInputRef"
              type="text"
              class="top-search-input"
              placeholder="搜索资料标题/关键词"
              @focus="onSearchFocus"
              @blur="onSearchBlur"
              @keydown.enter="performSearch"
              @input="onInputShowHistory"
            />
            <button type="button" @click="showHistoryDropdown" style="background:transparent;border:none;outline:none;position:absolute;right:6.2rem;top:50%;transform:translateY(-50%);z-index:10000;padding:0 0.5rem;cursor:pointer;">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
            </button>
            <button @click="performSearch" class="search-button">
              <svg xmlns="http://www.w3.org/2000/svg" class="search-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              搜索
            </button>
            <div v-show="showHistory" class="top-search-history-list" :style="historyDropdownStyle">
              <div v-for="(item, idx) in searchHistory" :key="item" class="top-search-history-item-wrapper">
                <div class="top-search-history-item"
                  @mousedown="historyClicking = true"
                  @mouseup="historyClicking = false"
                  @touchstart="historyClicking = true"
                  @touchend="historyClicking = false"
                >
                  <div style="flex:1;min-width:0;overflow:hidden;display:flex;align-items:center;" @click.prevent="selectHistory(item)">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V4a2 2 0 10-4 0v1.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" /></svg>
                    <span style="white-space:nowrap;overflow:hidden;text-overflow:ellipsis;">{{ item }}</span>
                  </div>
                  <button class="history-delete-btn" @click.stop.prevent="deleteHistoryItem(item)" aria-label="删除此条历史">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                  </button>
                </div>
                <div v-if="idx < searchHistory.length - 1" class="top-search-history-divider"></div>
              </div>
              <div class="top-search-history-clear">
                <button class="top-search-history-clear-btn" @mousedown.prevent="clearSearchHistory">清空历史</button>
              </div>
            </div>
          </div>
        </div>
      </div>



      <!-- 主要内容区域 - 双列布局 -->
      <div class="main-content">
        <!-- 左侧筛选栏 -->
        <div class="sidebar" :class="{ 'sidebar-open': isSidebarOpen }">
  
        </div> 

        <!-- 移动端遮罩层 -->
        <div class="mobile-overlay" v-if="isSidebarOpen" @click="closeSidebar"></div>

        <!-- 右侧内容区域 -->
        <div class="content-area">
          <!-- 加载状态 -->
          <div v-if="loading" class="loading-state">
            <div class="loading-spinner"></div>
            <span>加载中...</span>
          </div>

          <!-- 资料卡片列表 -->
          <div v-else class="documents-grid">
            <div v-for="doc in filteredDocuments" :key="doc.id" class="document-card">
              <div class="document-content">
                <div class="document-header">
                  <div class="document-icon-wrapper">
                    <DocumentIcon class="document-icon" />
                  </div>
                  <div class="document-badge">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd" />
                    </svg>
                    <span class="badge-text">{{ doc.creditCost }}</span>
                  </div>
                </div>
                <h2 class="document-title">{{ doc.fileName }}</h2>
                <p class="document-description">{{ doc.description }}</p>
                <div class="document-meta">
                  <div class="meta-item">
                    <svg class="meta-icon" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                    </svg>
                    <span>{{ doc.uploaderName }}</span>
                  </div>
                  <div class="meta-item">
                    <svg class="meta-icon" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                    </svg>
                    <span>{{ formatDate(doc.uploadedAt) }}</span>
                  </div>
                </div>
              </div>
              <div class="document-footer">
                <div class="footer-stats">
                  <span class="download-count">
                    <ArrowDownTrayIcon class="icon" />
                    <b>{{ doc.downLoaded }}</b> 次下载
                  </span>
                </div>
                <router-link :to="`/document/${doc.id}`" class="detail-link">查看详情</router-link>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="!loading && (!filteredDocuments || filteredDocuments.length === 0)" class="empty-state">
            <DocumentIcon class="empty-icon" />
            {{ search ? '未找到相关文档' : '暂无符合条件的资料' }}
          </div>

          <!-- 分页器 -->
          <div class="pagination" v-if="totalPages > 1">
            <button @click="prevPage" :disabled="page === 1" class="pagination-button prev">
              <svg xmlns="http://www.w3.org/2000/svg" class="pagination-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
              </svg>
              上一页
            </button>
            
            <button v-for="p in displayedPages" :key="p" @click="goToPage(p)" 
              :class="['pagination-button', 'page-number', p === page ? 'active' : '', p === -1 ? 'ellipsis' : '']" 
              :disabled="p === -1">
              <span v-if="p !== -1">{{ p }}</span>
              <span v-else class="ellipsis-text">...</span>
            </button>
            
            <button @click="nextPage" :disabled="page === totalPages" class="pagination-button next">
              下一页
              <svg xmlns="http://www.w3.org/2000/svg" class="pagination-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { ArrowDownTrayIcon, DocumentIcon } from '@heroicons/vue/24/outline'
import { handleAuth, getUserInfo } from '@/api/auth'
import { get } from '@/api/requests'
// import { useRoute, useRouter } from 'vue-router'

// const route = useRoute()
// const router = useRouter()
const userInfo = ref(getUserInfo())

// 移动端侧边栏状态
const isSidebarOpen = ref(false)

// 防抖函数
function debounce(func: Function, wait: number) {
  let timeout: any
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 文件列表数据
interface FileData {
  id: string
  fileName: string
  description: string
  creditCost: number
  downLoaded: number
  uploadedAt: string
  uploaderName: string
}

// interface SearchResponse {
//   total: number
//   page: number
//   pageSize: number
//   data: FileData[]
// }

const documents = ref<FileData[]>([])
const total = ref(0)
const page = ref(1)
const pageSize = ref(10)
const search = ref('')
const showHistory = ref(false)
const searchInputRef = ref<HTMLInputElement | null>(null)
const searchHistory = ref<string[]>([])
const historyClicking = ref(false)
const historyDropdownStyle = ref<Record<string, any>>({ left: '0px', top: '120px', width: '100vw', zIndex: 99999 })
const sortBy = ref('newest')
const loading = ref(false)

const filteredDocuments = computed(() => {
  // 搜索现在由后端处理，直接返回 documents
  // 添加空值检查，确保始终返回数组
  return documents.value || []
})

// const activeFilterCount = computed(() => {
//   let count = 0
//   if (search.value.trim() !== '') count++
//   if (sortBy.value !== 'newest') count++
//   return count
// })

// 移动端侧边栏控制
// const toggleSidebar = () => {
//   isSidebarOpen.value = !isSidebarOpen.value
//   // 防止背景滚动
//   if (isSidebarOpen.value) {
//     document.body.style.overflow = 'hidden'
//   } else {
//     document.body.style.overflow = ''
//   }
// }

const closeSidebar = () => {
  isSidebarOpen.value = false
  document.body.style.overflow = ''
}

// 获取文件列表
const fetchDocuments = async () => {
  try {
    loading.value = true
    
    // 构建查询参数
    const params: any = {
      page: page.value,
      pageSize: pageSize.value
    }
    
    // 如果有搜索关键词，添加到参数中
    if (search.value.trim()) {
      params.keyword = search.value.trim()
    }
    
    // 根据排序方式添加排序参数
    if (sortBy.value === 'downloads') {
      params.sortBy = 'downloads'
      params.sortOrder = 'desc'
    } else if (sortBy.value === 'points') {
      params.sortBy = 'creditCost'
      params.sortOrder = 'asc'
    } else {
      // 默认按最新上传排序
      params.sortBy = 'uploadedAt'
      params.sortOrder = 'desc'
    }
    
    console.log('Fetching documents with params:', params) // 调试日志
    
    const response = await get('/file/search', { params })
    
    console.log('API response:', response) // 调试日志
    
    // 添加空值检查，防止 undefined 错误
    if (response && (response as any).data) {
      documents.value = (response as any).data
      total.value = (response as any).total || 0
      // 确保页码不超出范围
      if (page.value > totalPages.value && totalPages.value > 0) {
        page.value = totalPages.value
      }
    } else {
      // 如果响应为空或没有数据，设置为空数组
      documents.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('Failed to fetch documents:', error)
    // 发生错误时设置为空数组
    documents.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 组件挂载时处理授权
onMounted(async () => {
  // 先处理授权
  await handleAuthorization()
  // 然后获取文件列表
  await fetchDocuments()
  loadSearchHistory() // 加载历史记录
  updateHistoryDropdownPosition() // 页面挂载时强制写入一条历史，并强制 showHistory = true 进行调试
})

// 处理授权
const handleAuthorization = async () => {
  // 从完整 URL 中提取 code 参数
  const url = new URL(window.location.href)
  const code = url.searchParams.get('code')
  
  if (code) {
    try {
      const response = await handleAuth(code)
      if (response) {
        userInfo.value = {
          stu_Name: response.stu_Name,
          stu_Nickname: response.stu_Nickname,
          stu_Avator: response.stu_Avator,
          stu_Id: response.stu_Id,
          stu_section: response.stu_section,
          token: response.token
        }
        // 清除 URL 中的 code 参数
        const newUrl = window.location.pathname
        window.history.replaceState({}, '', newUrl)
      }
    } catch (error) {
      console.error('Authorization failed:', error)
    }
  }
}

const totalPages = computed(() => {
  const pages = Math.ceil(total.value / pageSize.value)
  return Math.max(pages, 1) // 至少返回1页
})

const displayedPages = computed(() => {
  const pages: number[] = []
  const currentPage = page.value
  const total = totalPages.value
  
  if (total <= 7) {
    // 如果总页数小于等于7，显示所有页码
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // 如果总页数大于7，显示部分页码
    // 始终显示第一页
    pages.push(1)
    
    if (currentPage <= 4) {
      // 当前页在前4页，显示1-5页
      for (let i = 2; i <= 5; i++) {
        pages.push(i)
      }
      pages.push(-1) // 省略号
      pages.push(total)
    } else if (currentPage >= total - 3) {
      // 当前页在后4页，显示最后5页
      pages.push(-1) // 省略号
      for (let i = total - 4; i <= total; i++) {
        pages.push(i)
      }
    } else {
      // 当前页在中间，显示当前页前后各2页
      pages.push(-1) // 省略号
      for (let i = currentPage - 2; i <= currentPage + 2; i++) {
        pages.push(i)
      }
      pages.push(-1) // 省略号
      pages.push(total)
    }
  }
  
  return pages
})

function goToPage(p: number) {
  if (p > 0 && p <= totalPages.value && p !== page.value) {
    page.value = p
    // 滚动到页面顶部
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }
}

function prevPage() {
  if (page.value > 1) {
    page.value--
    // 滚动到页面顶部
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }
}

function nextPage() {
  if (page.value < totalPages.value) {
    page.value++
    // 滚动到页面顶部
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }
}

function formatDate(dateString: string) {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 使用防抖优化搜索
const debouncedSearch = debounce(() => {
  page.value = 1
  fetchDocuments()
  // 滚动到页面顶部
  window.scrollTo({ top: 0, behavior: 'smooth' })
}, 500)

// 加载历史
function loadSearchHistory() {
  const raw = localStorage.getItem('searchHistory')
  searchHistory.value = raw ? JSON.parse(raw) : []
}

// 保存历史
function saveSearchHistory(keyword: string) {
  if (!keyword.trim()) return
  let history = searchHistory.value.filter(item => item !== keyword.trim())
  history.unshift(keyword.trim())
  if (history.length > 10) history = history.slice(0, 10)
  searchHistory.value = history
  localStorage.setItem('searchHistory', JSON.stringify(history))
}

// 清空历史
function clearSearchHistory() {
  searchHistory.value = []
  localStorage.removeItem('searchHistory')
}

// 点击历史记录
function selectHistory(keyword: string) {
  search.value = keyword
  showHistory.value = false
  nextTick(() => performSearch())
  historyClicking.value = false
}

// 删除历史记录项
function deleteHistoryItem(item: string) {
  const idx = searchHistory.value.indexOf(item)
  if (idx !== -1) {
    searchHistory.value.splice(idx, 1)
    localStorage.setItem('searchHistory', JSON.stringify(searchHistory.value))
  }
}

// 搜索框聚焦时展示历史
function onSearchFocus() {
  loadSearchHistory()
  showHistory.value = true
  updateHistoryDropdownPosition()
}
function onSearchBlur() {
  setTimeout(() => {
    if (!historyClicking.value) showHistory.value = false
  }, 150)
}

// 手动搜索函数
const performSearch = () => {
  if (search.value.trim()) saveSearchHistory(search.value)
  // 若历史为空，主动写入一条测试数据，便于调试
  if (!localStorage.getItem('searchHistory')) {
    saveSearchHistory('测试历史')
    loadSearchHistory()
  }
  page.value = 1
  fetchDocuments()
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

// 监听搜索变化（使用防抖）
watch(search, () => {
  debouncedSearch()
})

// 监听排序变化（立即执行）
watch(sortBy, () => {
  page.value = 1
  fetchDocuments()
  // 滚动到页面顶部
  window.scrollTo({ top: 0, behavior: 'smooth' })
})

// 监听页码变化
watch(page, () => {
  fetchDocuments()
}, { immediate: false })

function showHistoryDropdown() {
  loadSearchHistory()
  showHistory.value = true
  nextTick(() => {
    searchInputRef.value?.focus()
    updateHistoryDropdownPosition()
  })
}

function onInputShowHistory() {
  loadSearchHistory()
  showHistory.value = true
  updateHistoryDropdownPosition()
}

function updateHistoryDropdownPosition() {
  nextTick(() => {
    const input = searchInputRef.value
    if (input) {
      const rect = input.getBoundingClientRect()
      historyDropdownStyle.value = {
        left: rect.left + 'px',
        top: rect.bottom + 'px',
        width: rect.width + 'px',
        zIndex: 99999,
        position: 'fixed',
      }
    }
  })
}
</script>

<style scoped>
/* 基本样式和布局 */
.page-container {
  min-height: 100vh;
  background: #fff;
  padding: 2rem 0;
  position: relative;
}

.container {
  max-width: 1400px; /* 确保内容在桌面居中且有最大宽度 */
  margin: 0 auto;
  padding: 0 2rem; /* 左右内边距 */
  position: relative;
  z-index: 1;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
  animation: fadeIn 0.5s ease-in-out;
}

.main-content {
  display: grid;
  grid-template-columns: 320px 1fr; /* 侧边栏固定宽度，内容区自适应 */
  gap: 2rem;
  align-items: start;
}


.content-area {
  min-height: 600px;
}

.documents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.25rem;
  margin-bottom: 1rem;
}

.document-card {
  background: #fff !important;
  border-radius: 0.75rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.18);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  display: flex;
  flex-direction: column;
}

.document-content {
  background: #ffffff !important;
  flex: 1 1 auto;
  padding: 1rem; /* Added padding to content */
}

.document-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.document-icon-wrapper {
  width: 2.25rem;
  height: 2.25rem;
  background: #ffffff;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(13, 13, 13, 0.3);
}

.document-icon {
  width: 1.125rem;
  height: 1.125rem;
  color: #000000;
  transition: transform 0.3s ease;
}

.document-badge {
  background: #08aa8a;
  color: #d7db08;
  padding: 0.4rem 0.8rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  box-shadow: 0 2px 8px rgba(239, 239, 239, 0.3);
  border: 1px solid rgba(0,0,0,0.04);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  margin-left: auto;
}

.badge-text {
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.025em;
}


.document-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: color 0.3s ease;
}


.document-description {
  color: #64748b;
  font-size: 0.8rem;
  margin-bottom: 0.75rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: color 0.3s ease;
}

.document-card:hover .document-description {
  color: #475569;
}
.document-meta {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-size: 0.8rem;
  color: #94a3b8;
  transition: color 0.3s ease;
}

.meta-icon {
  width: 0.8rem;
  height: 0.8rem;
  color: #1d8a1d;
}

.document-card:hover .meta-item {
  color: #64748b;
}

.document-footer {
  background: #fff !important;
  border-top: 1px solid #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background 0.3s ease;
  padding: 1rem; /* Added padding to footer */
}

.document-card:hover .document-footer {
  background: linear-gradient(to bottom, #f1f5f9, #e2e8f0);
}

.footer-stats {
  display: flex;
  align-items: center;
}

.download-count {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  color: #64748b;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.document-card:hover .download-count {
  color: #475569;
}

.icon {
  width: 0.8rem;
  height: 0.8rem;
  transition: transform 0.3s ease;
}

.document-card:hover .icon {
  transform: scale(1.1);
}

.detail-link {
  color: #000000;
  font-weight: 600;
  text-decoration: none;
  padding: 0.3rem 0.6rem;
  border-radius: 0.5rem;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  background: rgba(11, 12, 12, 0.08);
  border: 1px solid rgba(14, 226, 155, 0.18);
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.12);
  position: relative;
  overflow: hidden;
}


.detail-link {
  background: #10b981;
  color: #ffffff;
  border-color: #afb3b1;
  box-shadow: 0 0 15px rgba(16, 185, 129, 0.2), 0 0 30px rgba(16, 185, 129, 0.1);
}

.detail-link:hover::before {
  left: 100%;
}

.empty-state {
  text-align: center;
  padding: 4rem 1rem;
  color: #64748b;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  background: #fff !important;
  backdrop-filter: none !important;
  border-radius: 1.5rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.04);
}

.empty-icon {
  width: 4rem;
  height: 4rem;
  color: #94a3b8;
}

.pagination-info {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 1rem;
  margin-bottom: 1rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 1rem;
  padding: 0.75rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.pagination-stats {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.current-page {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.total-pages {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.total-items {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-top: 2rem;
  flex-wrap: wrap;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 1rem;
  padding: 1rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.pagination-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: 0.75rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.3s ease;
  min-width: 2.5rem;
  height: 2.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.pagination-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.5s ease;
}

.pagination-button:hover:not(:disabled)::before {
  left: 100%;
}


.pagination-button.active {
  background: rgb(41, 150, 41);
  border-color:rgb(70, 181, 104);
  color: white;
  font-weight: 700;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
  transform: translateY(-1px);
}

.pagination-button.active::before {
  display: none;
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.pagination-button.ellipsis {
  background: transparent;
  border-color: transparent;
  color: #94a3b8;
  font-weight: normal;
  cursor: default;
  box-shadow: none;
  min-width: 2rem;
}

.pagination-button.ellipsis:hover {
  background: transparent;
  border-color: transparent;
  transform: none;
  box-shadow: none;
}

.ellipsis-text {
  font-size: 1.2rem;
  font-weight: bold;
  color: #94a3b8;
}

.pagination-icon {
  width: 1rem;
  height: 1rem;
  transition: transform 0.3s ease;
}

.pagination-button:hover:not(:disabled) .pagination-icon {
  transform: scale(1.1);
}

.pagination-button.prev {
  padding-left: 1rem;
  padding-right: 1.25rem;
}

.pagination-button.next {
  padding-left: 1.25rem;
  padding-right: 1rem;
}

.page-number {
  min-width: 2.5rem;
  padding: 0.75rem 0.5rem;
}

/* 响应式分页器 */
@media (max-width: 768px) {
  .pagination {
    gap: 0.25rem;
    padding: 0.75rem;
  }
  
  .pagination-button {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    min-width: 2rem;
    height: 2rem;
  }
  
  .pagination-button.prev,
  .pagination-button.next {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }
  
  .page-number {
    min-width: 2rem;
    padding: 0.5rem 0.25rem;
  }
  
  .pagination-icon {
    width: 0.875rem;
    height: 0.875rem;
  }
}

@media (max-width: 480px) {
  .pagination {
    gap: 0.25rem;
    padding: 0.5rem;
  }
  
  .pagination-button {
    padding: 0.5rem 0.5rem;
    font-size: 0.75rem;
    min-width: 1.75rem;
    height: 1.75rem;
  }
  
  .pagination-button.prev,
  .pagination-button.next {
    padding: 0.5rem 0.75rem;
    font-size: 0.7rem;
  }
  
  .page-number {
    min-width: 1.75rem;
    padding: 0.5rem 0.25rem;
  }
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #64748b;
  gap: 1rem;
  background: #fff !important;
  backdrop-filter: none !important;
  border-radius: 1.5rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.04);
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 3px solid rgba(102, 126, 234, 0.2);
  border-top-color: #1eb96c;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 280px 1fr;
    gap: 1.5rem;
  }
  
  .documents-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }
  
  .title {
    font-size: 2rem;
    margin-bottom: 0.4rem;
  }
  
  .subtitle {
    font-size: 1rem;
    margin-top: 0.2rem;
    margin-bottom: 0.75rem;
  }
  
  .top-search-bar {
    margin-top: 1rem;
    display: flex;
    justify-content: center;
  }
  
  .search-input-container {
    display: flex;
    align-items: center;
    background: #f6fefb;
    border-radius: 1.5rem;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.06);
    overflow: hidden;
    max-width: 500px; /* Kept for general large screen control, but made it responsive */
    width: 100%;
    border: 2px solid #e0f7ee;
    padding: 0.15rem 0.25rem;
  }
  
  .top-search-input {
    flex: 1;
    padding: 0.85rem 1.2rem;
    border: none;
    outline: none;
    font-size: 1rem;
    color: #1e293b;
    background: transparent;
    border-radius: 1.5rem;
    background: #f6fefb;
    transition: border 0.2s, box-shadow 0.2s;
  }
  
  .top-search-input:focus {
    border: 2px solid #10b981;
    box-shadow: 0 0 0 2px #baf5e0;
    background: #fff;
  }
  
  .top-search-input::placeholder {
    color: #b0bfc7;
  }
  
  .search-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.85rem 1.5rem;
    background: #10b981;
    color: #fff;
    border: none;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    border-radius: 1.5rem;
    margin-left: 0.5rem;
    transition: background 0.2s, box-shadow 0.2s;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.08);
  }
  
  .search-button:hover {
    background: #0e9e6e;
    box-shadow: 0 4px 16px rgba(16, 185, 129, 0.15);
  }
  
  .search-icon {
    width: 1.25rem;
    height: 1.25rem;
    color: #fff;
  }
  
  /* 侧边栏搜索框美化 */
  .search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
  }
  
  .search-input {
    width: 100%;
    padding: 0.85rem 1.2rem;
    border: none;
    border-radius: 1.5rem;
    font-size: 1rem;
    background: #f6fefb;
    color: #1e293b;
    transition: border 0.2s, box-shadow 0.2s;
  }
  
  .search-input:focus {
    border: 2px solid #10b981;
    box-shadow: 0 0 0 2px #baf5e0;
    background: #fff;
  }
  
  .search-input::placeholder {
    color: #b0bfc7;
  }
  
  .clear-button {
    position: absolute;
    right: 0.75rem;
    padding: 0.5rem;
    color: #b0bfc7;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
  }
  
  .clear-button:hover {
    color: #10b981;
    background: #e0f7ee;
  }
  
  .clear-icon {
    width: 1.25rem;
    height: 1.25rem;
    transition: transform 0.3s ease;
  }
  
  /* 显示移动端筛选按钮 */
  .mobile-filter-toggle {
    display: flex;
  }
  
  /* 移动端布局调整 */
  .main-content {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .sidebar {
    position: fixed;
    top: 0;
    left: -100%;
    width: 85%;
    max-width: 350px;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    z-index: 1000;
    padding: 2rem 1.5rem;
    overflow-y: auto;
    transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 0 50px rgba(0, 0, 0, 0.3);
  }
  
  .sidebar-open {
    left: 0;
  }
  
  /* 显示移动端关闭按钮 */
  .mobile-close-btn {
    display: flex;
  }
  
  /* 移动端遮罩层 */
  .mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    backdrop-filter: blur(4px);
  }
  
  .content-area {
    order: 1;
  }
  
  .documents-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 0.75rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  /* 移动端卡片优化 */
  .document-card {
    margin: 0;
  }
  
  .document-content {
    padding: 0.6rem; /* Adjust padding for mobile */
  }
  
  .document-footer {
    padding: 0.5rem;
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }
  
  .detail-link {
    text-align: center;
    padding: 0.5rem;
  }
  
  /* 移动端分页优化 */
  .pagination {
    gap: 0.25rem;
  }
  
  .pagination-button {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    min-width: 2.5rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.75rem;
  }
  
  .title {
    font-size: 1.75rem;
    margin-bottom: 0.3rem;
  }
  
  .subtitle {
    font-size: 0.875rem;
    margin-top: 0.15rem;
    margin-bottom: 0.5rem;
  }
  
  .top-search-bar {
    margin-top: 0.5rem;
  }
  
  .top-search-input {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
  }
  
  .search-button {
    padding: 0.5rem 0.875rem;
    font-size: 0.85rem;
  }
  
  .sidebar {
    width: 95%;
    padding: 1.5rem 1rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .document-card {
    margin: 0 -0.25rem;
  }
  
  .document-content {
    padding: 0.5rem; /* Further adjust padding for smaller mobile */
  }
  
  .document-footer {
    padding: 0.5rem;
  }
  
  .document-title {
    font-size: 0.9rem;
  }
  
  .document-description {
    font-size: 0.75rem;
  }
  
  .pagination-button {
    padding: 0.5rem 0.5rem;
    font-size: 0.8rem;
    min-width: 2rem;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .document-card:hover {
    transform: none;
  }
  
  .document-card:active {
    transform: scale(0.98);
  }
  
  .search-card:hover, /* Assuming these are from sidebar, not directly in this code */
  .sort-card:hover,
  .stats-card:hover {
    transform: none;
  }
  
  .detail-link:hover {
    transform: none;
  }
  
  .detail-link:active {
    transform: scale(0.95);
  }
  
  .pagination-button:hover:not(:disabled) {
    transform: none;
  }
  
  .pagination-button:active:not(:disabled) {
    transform: scale(0.95);
  }
}

/* 电脑端搜索框优化 - 修改为与手机版一致 */
@media (min-width: 1024px) {
  .top-search-bar {
    margin-top: 1rem; /* 调整为与手机版一致的间距 */
    display: flex;
    justify-content: center;
  }
  .search-input-container {
    max-width: 100%; /* 允许它填满可用宽度 */
    /* 移除 margin: 0 auto; 让它自然对齐或由父元素控制 */
    padding: 0.15rem 0.25rem; /* 与手机版一致的内边距 */
  }
  .top-search-input {
    font-size: 1rem; /* 与手机版一致的字体大小 */
    padding: 0.85rem 1.2rem; /* 与手机版一致的内边距 */
  }
  .search-button {
    font-size: 1rem; /* 与手机版一致的字体大小 */
    padding: 0.85rem 1.5rem; /* 与手机版一致的内边距 */
    margin-left: 0.5rem; /* 与手机版一致的左边距 */
  }
}

/* 移动端优化（已存在，补充说明） */
@media (max-width: 768px) {
  .search-input-container {
    max-width: 100%;
    padding: 0.1rem 0.1rem;
  }
  .top-search-input {
    font-size: 0.95rem;
    padding: 0.7rem 1rem;
  }
  .search-button {
    font-size: 0.95rem;
    padding: 0.7rem 1.2rem;
    margin-left: 0.3rem;
  }
}

@media (min-width: 1024px) {
  .documents-grid {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 1.5rem;
    /* 移除以下可能导致桌面显示bug的样式 */
    /* max-width: 1200px; */
    /* margin-left: auto; */
    /* margin-right: auto; */
  }
  .document-card {
    border-radius: 1.1rem;
    box-shadow: 0 4px 24px rgba(0,0,0,0.13);
    transition: box-shadow 0.2s, transform 0.2s;
  }
  .document-card:hover {
    box-shadow: 0 8px 32px rgba(16,185,129,0.18);
    transform: translateY(-2px) scale(1.03);
  }
  .document-title {
    font-size: 1.15rem;
  }
  .document-description {
    font-size: 0.95rem;
  }
}

.top-search-history-list {
  position: fixed;
  /* left/top/width 由js动态设置 */
  z-index: 99999;
  background: #fff;
  border: 1.5px solid #e5e7eb;
  border-radius: 0.7rem;
  box-shadow: 0 2px 10px rgba(16,185,129,0.07);
  max-height: 180px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0.15rem 0;
  animation: fadeIn 0.2s;
  font-size: 0.97rem;
}
.top-search-history-item {
  padding: 0.45rem 1rem;
  font-size: 0.97rem;
  color: #1e293b;
  cursor: pointer;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  -webkit-tap-highlight-color: transparent;
}
.top-search-history-item:hover {
  background: #f6fefb;
}
.top-search-history-clear {
  text-align: right;
  padding: 0.3rem 1.2rem 0.2rem 1.2rem;
}
.top-search-history-clear-btn {
  color: #10b981;
  font-size: 0.95rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  transition: color 0.2s;
}
.top-search-history-clear-btn:hover {
  color: #e11d48;
}
@media (max-width: 768px) {
  .top-search-history-list {
    left: 0;
    right: 0;
    min-width: 0;
    width: 100%;
    font-size: 0.97rem;
    z-index: 9999;
    border-radius: 0.6rem;
    padding: 0.1rem 0;
  }
  .top-search-history-item {
    font-size: 0.97rem;
    padding: 0.45rem 0.8rem;
  }
}
.history-delete-btn {
  background: none;
  border: none;
  outline: none;
  padding: 0.3rem 0.5rem;
  margin-left: 0.2rem;
  cursor: pointer;
  border-radius: 0.3rem;
  transition: background 0.15s;
  display: flex;
  align-items: center;
}
.history-delete-btn:hover {
  background: #fef2f2;
}
@media (max-width: 768px) {
  .history-delete-btn {
    padding: 0.5rem 0.7rem;
    margin-left: 0.1rem;
  }
}
.top-search-history-item-wrapper {
  width: 100%;
}
.top-search-history-divider {
  width: 92%;
  margin: 0 auto;
  border-bottom: 1px solid #f1f5f9;
  height: 0;
}
@media (min-width: 1024px) {
  .pagination-button {
    font-size: 1.05rem;
    min-width: 3rem;
    height: 3rem;
    padding: 1rem 1.5rem;
  }
}
</style>
