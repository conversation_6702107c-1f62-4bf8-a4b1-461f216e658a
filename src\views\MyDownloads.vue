<template>
  <div class="min-h-screen bg-white py-4 px-4">
    <div class="max-w-3xl mx-auto">
      <!-- 顶部导航 -->
      <div class="flex items-center gap-3 mb-6">
        <button @click="$router.back()" class="p-2 rounded-full bg-white/80 backdrop-blur-sm shadow-sm hover:bg-white transition-all">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
          </svg>
        </button>
        <h1 class="text-xl font-bold text-blue-800">我的下载</h1>
      </div>

      <!-- 内容区域 -->
      
        <div v-if="loading" class="flex flex-col items-center justify-center py-12">
          <div class="w-8 h-8 border-4 border-blue-200 border-t-blue-500 rounded-full animate-spin mb-3"></div>
          <span class="text-sm text-gray-500">加载中...</span>
        </div>

        <div v-else-if="downloads.length" class="space-y-3">
          <div v-for="item in downloads" :key="item.id" 
               class="bg-white rounded-xl p-4 shadow-sm border border-blue-100 hover:shadow-md transition-all cursor-pointer hover:bg-blue-50/50 group"
               @click="goToDetail(item.fileId || item.id)">
            <div class="flex flex-col gap-2">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <h3 class="font-semibold text-gray-900 text-base group-hover:text-blue-700 transition-colors">{{ item.fileName }}</h3>
                  <div class="flex items-center gap-2 mt-1">
                    <span class="px-2 py-0.5 rounded-full bg-blue-50 text-blue-600 text-xs">已下载</span>
                    <span class="text-xs text-gray-500">{{ formatDate(item.downloadTime) }}</span>
                  </div>
                </div>
                <div class="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                </div>
              </div>
              
              <div class="flex items-center gap-1 text-xs text-gray-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd" />
                </svg>
                <span>消耗积分：{{ item.cost }}</span>
              </div>
              
              <!-- 操作按钮区域 -->
              <div class="flex justify-end mt-3 pt-2 border-t border-gray-100">
                <button @click.stop="goToDetail(item.fileId || item.id)" 
                        class="px-3 py-1.5 rounded-lg bg-blue-50 text-blue-600 hover:bg-blue-100 transition-colors text-xs font-medium">
                  查看详情
                </button>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="flex flex-col items-center justify-center py-12 text-gray-500">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-300 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
          <span class="text-sm">暂无下载记录</span>
        </div>
      
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { get } from '@/api/requests'

const router = useRouter()
const downloads = ref<any[]>([])
const loading = ref(true)

onMounted(async () => {
  // 滚动到页面顶部
  window.scrollTo({ top: 0, behavior: 'auto' })
  
  try {
    const res = await get('/getInfo/getDownLoadLog')
    console.log('完整res:', res)
    downloads.value = Array.isArray(res) ? res : (Array.isArray(res.data) ? res.data : [])
  } catch (e) {
    downloads.value = []
  } finally {
    loading.value = false
  }
})

function formatDate(dateStr: string) {
  if (!dateStr) return ''
  const d = new Date(dateStr)
  return d.toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' })
}

function goToDetail(fileId: number) {
  if (fileId) {
    router.push(`/document/${fileId}`)
  }
}
</script> 
