<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 py-4 px-4">
    <div class="max-w-5xl mx-auto">
      <!-- 页面标题 -->
      <div class="text-center mb-4">
        <h1 class="text-2xl font-bold text-gray-800 mb-1">管理员审核</h1>
      </div>

      <!-- 数据统计面板 -->
      <div v-if="currentUser?.role === 'Admin'" class="mb-4">
        <button 
          @click="openStatsModal" 
          class="w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-2xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center gap-3"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          <span class="text-lg font-semibold">查看数据统计</span>
        </button>
      </div>

      <!-- 审核状态切换 -->
      <div class="flex justify-center gap-4 mb-4">
        <button 
          @click="currentType = 0" 
          :class="[
            'px-8 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:-translate-y-1',
            currentType === 0 
              ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-lg' 
              : 'bg-white text-gray-600 border-2 border-gray-200 hover:border-orange-300 hover:text-orange-600'
          ]"
        >
          待处理 ({{ pendingCount }})
        </button>
        <button 
          @click="currentType = 1" 
          :class="[
            'px-8 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:-translate-y-1',
            currentType === 1 
              ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg' 
              : 'bg-white text-gray-600 border-2 border-gray-200 hover:border-green-300 hover:text-green-600'
          ]"
        >
          已处理 ({{ approvedCount }})
        </button>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="flex flex-col items-center justify-center py-8">
        <div class="w-10 h-10 border-4 border-gray-200 border-t-green-500 rounded-full animate-spin mb-3"></div>
        <span class="text-gray-600 font-medium text-sm">加载中...</span>
      </div>

      <!-- 审核列表 -->
      <div v-else class="space-y-4">
        <div v-for="audit in auditList" :key="audit.auditId" class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300">
          <!-- 卡片头部 -->
          <div class="p-4 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="flex items-center gap-3 mb-2">
                  <div class="p-1.5 bg-blue-100 rounded-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
              </div>
                  <h3 class="text-lg font-bold text-gray-800">文件ID: {{ audit.fileId }}</h3>
            </div>
                <div class="flex flex-wrap gap-4 text-sm text-gray-600">
                  <div class="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <span>投诉人: {{ audit.stuName }} ({{ audit.nickName }})</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span>投诉时间: {{ formatDate(audit.subTime) }}</span>
                  </div>
                </div>
              </div>
              <div class="flex-shrink-0">
                <span :class="[
                  'px-4 py-2 rounded-full text-sm font-semibold',
                  audit.isHandled 
                    ? 'bg-green-100 text-green-700 border border-green-200' 
                    : 'bg-orange-100 text-orange-700 border border-orange-200'
                ]">
              {{ getStatusText(audit.isHandled) }}
                </span>
              </div>
            </div>
          </div>

          <!-- 卡片内容 -->
          <div class="p-4">
            <div class="space-y-3 mb-4">
              <div class="bg-gray-50 rounded-lg p-3">
                <div class="flex items-start gap-2">
                  <div class="p-1.5 bg-red-100 rounded-lg flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
              </div>
                  <div class="flex-1">
                    <h4 class="font-semibold text-gray-800 text-sm mb-0.5">投诉原因</h4>
                    <p class="text-gray-700 text-sm">{{ audit.reason || '暂无原因' }}</p>
              </div>
                </div>
              </div>

              <div class="bg-blue-50 rounded-lg p-3">
                <div class="flex items-start gap-2">
                  <div class="p-1.5 bg-blue-100 rounded-lg flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <div class="flex-1">
                    <h4 class="font-semibold text-gray-800 text-sm mb-0.5">被投诉人学号</h4>
                    <p class="text-gray-700 font-mono text-sm">{{ audit.stuId }}</p>
                  </div>
                </div>
              </div>

              <div v-if="audit.isHandled" class="bg-green-50 rounded-lg p-3">
                <div class="flex items-start gap-2">
                  <div class="p-1.5 bg-green-100 rounded-lg flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div class="flex-1">
                    <h4 class="font-semibold text-gray-800 text-sm mb-0.5">审核人</h4>
                    <p class="text-gray-700 text-sm">{{ audit.auditorName || '无' }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 审核操作区域 -->
            <div v-if="!audit.isHandled && canAudit(audit)" class="flex gap-2 justify-center">
              <button @click="viewFile(audit.fileId)" class="flex items-center gap-1.5 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-all duration-300 transform hover:-translate-y-1 shadow-md text-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                下载
              </button>
              <button @click="openAuditModal(audit)" class="flex items-center gap-1.5 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-all duration-300 transform hover:-translate-y-1 shadow-md text-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                通过
              </button>
              <button @click="openAuditModal(audit, false)" class="flex items-center gap-1.5 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-all duration-300 transform hover:-translate-y-1 shadow-md text-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
                淘汰
              </button>
            </div>

            <!-- 不能审核自己投诉的提示 -->
            <div v-if="!audit.isHandled && !canAudit(audit)" class="space-y-3">
              <div class="flex gap-2 flex-wrap">
                <button @click="viewFile(audit.fileId)" class="flex items-center gap-1.5 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-all duration-300 transform hover:-translate-y-1 shadow-md text-sm">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                下载查看文件
              </button>
              </div>
              <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div class="flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <span class="text-yellow-800 font-medium text-sm">不能审核自己的投诉</span>
            </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="auditList.length === 0" class="text-center py-8">
          <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          </div>
          <h3 class="text-lg font-semibold text-gray-600 mb-1">
          {{ currentType === 0 ? '暂无待处理的投诉' : '暂无已处理的投诉' }}
          </h3>
          <p class="text-gray-500 text-sm">当前没有需要处理的投诉记录</p>
        </div>
      </div>

      <!-- 分页器 -->
      <div v-if="totalPages > 1" class="flex justify-center gap-2 mt-4">
        <button @click="prevPage" :disabled="page === 1" 
          class="px-3 py-1.5 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 text-sm">
          上一页
        </button>
        <button v-for="p in displayedPages" :key="p" @click="goToPage(p)" 
          :class="[
            'px-3 py-1.5 rounded-lg border transition-all duration-300 text-sm',
            p === page 
              ? 'bg-blue-500 text-white border-blue-500' 
              : 'border-gray-300 text-gray-700 hover:bg-gray-50',
            p === -1 ? 'cursor-default' : 'cursor-pointer'
          ]" 
          :disabled="p === -1">
          {{ p === -1 ? '...' : p }}
        </button>
        <button @click="nextPage" :disabled="page === totalPages" 
          class="px-3 py-1.5 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 text-sm">
          下一页
        </button>
      </div>
    </div>

    <!-- 审核模态框 -->
    <div v-if="showAuditModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-xl shadow-xl w-full max-w-sm mx-auto">
        <div class="p-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-bold text-gray-800">
              {{ isApproved ? '通过投诉' : '拒绝投诉' }}
            </h3>
            <button @click="closeAuditModal" class="text-gray-400 hover:text-gray-600 transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
          </div>
        
        <div class="p-4">
          <div class="space-y-3 mb-4">
            <div class="bg-gray-50 rounded-lg p-3">
              <h4 class="font-semibold text-gray-800 text-sm mb-1">文件信息</h4>
              <p class="text-gray-600 text-sm">文件ID: {{ currentAudit?.fileId }}</p>
              <p class="text-gray-600 text-sm">投诉人: {{ currentAudit?.stuName }} ({{ currentAudit?.nickName }})</p>
            </div>
            
            <div>
              <label class="block text-sm font-semibold text-gray-700 mb-1">处理意见</label>
            <textarea 
              v-model="auditComment" 
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 resize-none text-sm"
              placeholder="请输入处理意见..."
                rows="3"
            ></textarea>
          </div>
        </div>
        </div>
        
        <div class="p-4 border-t border-gray-200 flex gap-2">
          <button @click="closeAuditModal" 
            class="flex-1 px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-all duration-300 text-sm">
            取消
          </button>
          <button @click="submitAudit" :disabled="submitting" 
            class="flex-1 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 text-sm">
            {{ submitting ? '提交中...' : '确认提交' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 管理员统计弹窗 -->
    <div v-if="showStatsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-2xl shadow-xl w-full max-w-md mx-auto">
        <div class="p-6 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-xl font-bold text-gray-800">数据统计面板</h3>
            <button @click="closeStatsModal" class="text-gray-400 hover:text-gray-600 transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
          </div>
        
        <div class="p-6">
          <div class="space-y-4">
            <div class="flex justify-between items-center p-4 bg-blue-50 rounded-xl">
              <span class="font-semibold text-gray-700">总用户数</span>
              <span class="text-2xl font-bold text-blue-600">{{ adminStats.totalUsers }}</span>
        </div>
            <div class="flex justify-between items-center p-4 bg-green-50 rounded-xl">
              <span class="font-semibold text-gray-700">总文件数</span>
              <span class="text-2xl font-bold text-green-600">{{ adminStats.totalFiles }}</span>
        </div>
            <div class="flex justify-between items-center p-4 bg-orange-50 rounded-xl">
              <span class="font-semibold text-gray-700">待审核数</span>
              <span class="text-2xl font-bold text-orange-600">{{ adminStats.pendingAudits }}</span>
            </div>
            <div class="flex justify-between items-center p-4 bg-purple-50 rounded-xl">
              <span class="font-semibold text-gray-700">今日上传</span>
              <span class="text-2xl font-bold text-purple-600">{{ adminStats.filesUploadedToday }}</span>
            </div>
          </div>
        </div>
        
        <div class="p-6 border-t border-gray-200">
          <button @click="closeStatsModal" 
            class="w-full px-4 py-3 bg-gray-500 text-white rounded-xl hover:bg-gray-600 transition-all duration-300">
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { post, get } from '@/api/requests'

// 审核数据接口
interface AuditData {
  auditId: string
  reason: string
  fileId: string
  subTime: string
  nickName: string
  stuName: string
  stuId: string
  isHandled: boolean
  auditorName?: string
}

// interface AuditResponse {
//   total: number
//   page: number
//   pageSize: number
//   data: AuditData[]
// }

// 用户信息接口
interface UserInfo {
  stu_Name: string
  stu_Id: string
  stu_section: string
  stu_Avator?: string
  stu_Nickname?: string
  role?: string
  token?: string
}

// 响应式数据
const auditList = ref<AuditData[]>([])
const total = ref(0)
const page = ref(1)
const pageSize = ref(10)
const currentType = ref(0) // 0: 待审核, 1: 已审核
const loading = ref(false)
const submitting = ref(false)

// 统计数据
const stats = ref({
  totalComplaints: 0,
  pendingComplaints: 0,
  processedComplaints: 0,
  todayComplaints: 0,
  approvedComplaints: 0,
  rejectedComplaints: 0
})

// 模态框相关
const showAuditModal = ref(false)
const currentAudit = ref<AuditData | null>(null)
const isApproved = ref(true)
const auditComment = ref('')

// 当前用户信息
const currentUser = ref<UserInfo | null>(null)

// 管理员统计弹窗相关
const showStatsModal = ref(false)
const adminStats = ref({
  totalUsers: 0,
  totalFiles: 0,
  pendingAudits: 0,
  filesUploadedToday: 0
})

// 计算属性
const totalPages = computed(() => Math.ceil(total.value / pageSize.value))

const displayedPages = computed(() => {
  const pages: number[] = []
  for (let i = 1; i <= totalPages.value; i++) {
    if (i === 1 || i === totalPages.value || Math.abs(i - page.value) <= 1) {
      pages.push(i)
    } else if (pages[pages.length - 1] !== -1) {
      pages.push(-1) // -1 means ...
    }
  }
  return pages
})

const pendingCount = ref(0)
const approvedCount = ref(0)

// 获取当前用户信息
const getCurrentUser = () => {
  const userInfoStr = localStorage.getItem('userInfo')
  if (userInfoStr) {
    try {
      currentUser.value = JSON.parse(userInfoStr)
    } catch (e) {
      console.error('Error parsing user info:', e)
      currentUser.value = null
    }
  }
}

// 检查是否可以审核（不能审核自己的投诉）
const canAudit = (audit: AuditData) => {
  if (!currentUser.value) return false
  return currentUser.value.stu_Id !== audit.stuId
}

// 获取审核历史
const fetchAuditHistory = async () => {
  try {
    loading.value = true
    const response = await get('/audit/getAuditHistory', {
      params: {
        type: currentType.value,
        page: page.value,
        pageSize: pageSize.value
      }
    })
    auditList.value = (response as any).data
    total.value = (response as any).total
  } catch (error) {
    console.error('Failed to fetch audit history:', error)
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const fetchStats = async () => {
  try {
    const [pendingResponse, approvedResponse] = await Promise.all([
      get('/audit/getAuditHistory', { params: { type: 0, page: 1, pageSize: 1 } }),
      get('/audit/getAuditHistory', { params: { type: 1, page: 1, pageSize: 1 } })
    ])
    pendingCount.value = (pendingResponse as any).total
    approvedCount.value = (approvedResponse as any).total
  } catch (error) {
    console.error('Failed to fetch stats:', error)
  }
}

// 获取详细统计数据
const fetchDetailedStats = async () => {
  try {
    // 获取总投诉数
    const totalResponse = await get('/audit/getAuditHistory', { 
      params: { type: -1, page: 1, pageSize: 1 } // -1 表示获取所有
    })
    
    // 获取待处理数
    const pendingResponse = await get('/audit/getAuditHistory', { 
      params: { type: 0, page: 1, pageSize: 1 } 
    })
    
    // 获取已处理数
    const processedResponse = await get('/audit/getAuditHistory', { 
      params: { type: 1, page: 1, pageSize: 1 } 
    })
    
    // 获取今日新增数（这里需要后端支持，暂时用总数据）
    const todayResponse = await get('/audit/getAuditHistory', { 
      params: { type: -1, page: 1, pageSize: 1 } 
    })
    
    // 获取通过审核数（需要后端支持，暂时用已处理数的一半）
    const approvedCount = Math.floor((processedResponse as any).total / 2)
    
    // 获取拒绝审核数
    const rejectedCount = (processedResponse as any).total - approvedCount
    
    stats.value = {
      totalComplaints: (totalResponse as any).total,
      pendingComplaints: (pendingResponse as any).total,
      processedComplaints: (processedResponse as any).total,
      todayComplaints: (todayResponse as any).total, // 这里应该调用专门的今日统计API
      approvedComplaints: approvedCount,
      rejectedComplaints: rejectedCount
    }
  } catch (error) {
    console.error('Failed to fetch detailed stats:', error)
  }
}

// 获取管理员统计数据
const fetchAdminStats = async () => {
  try {
    const res = await get('/admin/stats')
    adminStats.value = res as any
  } catch (e) {
    // 可根据需要添加错误提示
  }
}

// 打开审核模态框
const openAuditModal = (audit: AuditData, approved: boolean = true) => {
  currentAudit.value = audit
  isApproved.value = approved
  auditComment.value = ''
  showAuditModal.value = true
}

// 关闭审核模态框
const closeAuditModal = () => {
  showAuditModal.value = false
  currentAudit.value = null
  auditComment.value = ''
}

// 提交审核
const submitAudit = async () => {
  if (!currentAudit.value) return
  
  try {
    submitting.value = true
    await post('/audit/subJudge', {
      auditId: currentAudit.value.auditId,
      isApproved: isApproved.value,
      comment: auditComment.value.trim()
    })
    
    closeAuditModal()
    await fetchAuditHistory()
    await fetchStats()
    await fetchDetailedStats()
  } catch (error) {
    console.error('Failed to submit audit:', error)
  } finally {
    submitting.value = false
  }
}

// 查看文件
const viewFile = async (fileId: string) => {
  try {
    // 首先获取文件信息以获取文件名
    const fileInfoResponse = await get(`/file/info/${fileId}`)
    const fileName = (fileInfoResponse as any).fileName || `file_${fileId}`
    
    // 下载文件
    const response = await get(`/file/download/${fileId}`, {
      responseType: 'blob'
    })
    
    // 创建下载链接
    const url = window.URL.createObjectURL(new Blob([response as any]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', fileName)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Download failed:', error)
    alert('下载失败，请重试')
  }
}

// 分页函数
const goToPage = (p: number) => {
  if (p > 0 && p <= totalPages.value) page.value = p
}

const prevPage = () => {
  if (page.value > 1) page.value--
}

const nextPage = () => {
  if (page.value < totalPages.value) page.value++
}

// 工具函数
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// const formatFileSize = (bytes: number) => {
//   if (bytes === 0) return '0 B'
//   const k = 1024
//   const sizes = ['B', 'KB', 'MB', 'GB']
//   const i = Math.floor(Math.log(bytes) / Math.log(k))
//   return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
// }

// const getStatusClass = (isHandled: boolean) => {
//   return isHandled ? 'approved' : 'pending'
// }

const getStatusText = (isHandled: boolean) => {
  return isHandled ? '已处理' : '待处理'
}

// 打开管理员统计弹窗
const openStatsModal = async () => {
  await fetchAdminStats()
  showStatsModal.value = true
}

// 关闭管理员统计弹窗
const closeStatsModal = () => {
  showStatsModal.value = false
}

// 监听器
watch([currentType, page], () => {
  fetchAuditHistory()
})

// 组件挂载
onMounted(() => {
  fetchAuditHistory()
  fetchStats()
  fetchDetailedStats()
  getCurrentUser()
})
</script>

<style scoped>
/* 简单的动画效果 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 确保模态框在最上层 */
.z-50 {
  z-index: 50;
}
</style> 