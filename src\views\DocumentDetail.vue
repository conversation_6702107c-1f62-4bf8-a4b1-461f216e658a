<template>
  <div class="page-container">
    <div class="max-w-container">
      <!-- 返回按钮 -->
      <button @click="$router.back()" class="back-button">
        <svg xmlns="http://www.w3.org/2000/svg" class="back-icon" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
        </svg>
        <span class="back-text">返回</span>
      </button>

      <!-- 主要内容区域 (简化为更少层叠) -->
      <div class="main-content-wrapper">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner"></div>
        </div>

        <template v-else-if="doc">
          <!-- 文件标题和描述区域 - 置顶 -->
          <div class="top-info-section">
            <h1 class="document-title-top">{{ doc.fileName }}</h1>
            <p class="document-description-top">{{ doc.description }}</p>

            <!-- 文件信息标签 (上传者、下载量、积分、上传时间) -->
            <div class="file-badges-container-top">
              <span class="info-badge user">
                <svg xmlns="http://www.w3.org/2000/svg" class="badge-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 0 1114 0H3z" clip-rule="evenodd" />
                </svg>
                {{ doc.uploaderName }}
              </span>
              <span class="info-badge download">
                <svg xmlns="http://www.w3.org/2000/svg" class="badge-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
                {{ doc.downLoaded }} 次下载
              </span>
              <span class="info-badge credit">
                <svg xmlns="http://www.w3.org/2000/svg" class="badge-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd" />
                </svg>
                {{ doc.creditCost }} 积分
              </span>
              <span class="info-badge time">
                <svg xmlns="http://www.w3.org/2000/svg" class="badge-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                </svg>
                {{ formatDate(doc.uploadedAt) }}
              </span>
            </div>
          </div>
          
          <!-- 文件预览区域 -->
          <div class="file-preview-section">
            <h2 class="section-title">
              <div class="section-icon-background">
                <svg xmlns="http://www.w3.org/2000/svg" class="section-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0113 2.586L16.414 6A2 2 0 0117 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 5a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm0 4a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              文件预览
            </h2>
            <div class="preview-content">
              <!-- 优先显示从预览接口获取的文本，否则显示doc.text，最后是占位符 -->
              <template v-if="previewContent">
                <p class="preview-text">{{ previewContent }}</p>
              </template>
              <template v-else-if="doc.text">
                <p class="preview-text">{{ doc.text }}</p>
              </template>
              <template v-else>
                <div class="preview-placeholder">
                  <p>该文件不支持预览</p>
      
                </div>
              </template>
            </div>
          </div>

          <!-- 下载和投诉按钮区域 -->
          <div class="action-buttons-section">
            <!-- 下载区域 -->
            <div class="download-area" @click="handleDownloadAreaClick">
              <div class="download-area-content">
                <div class="download-icon-wrapper">
                  <div class="download-icon-background">
                    <svg xmlns="http://www.w3.org/2000/svg" class="download-icon" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <div class="download-status-text">
                      <template v-if="userInfo.value && doc && userInfo.value.stu_Id === doc.uploaderId">
                        您是上传者，无需消耗积分
                      </template>
                      <template v-else-if="hasDownloaded">
                        您已下载过该文件
                      </template>
                      <template v-else>
                        点击下载文件
                      </template>
                    </div>
                    <div class="download-file-name">{{ doc.fileName }}</div>
                  </div>
                </div>
                <div class="download-credit-info">
                  <div class="download-credit-label">
                    <template v-if="userInfo.value && doc && userInfo.value.stu_Id === doc.uploaderId">
                      无需积分
                    </template>
                    <template v-else-if="hasDownloaded">
                      已下载
                    </template>
                    <template v-else>
                      所需积分
                    </template>
                  </div>
                  <div class="download-credit-cost">
                    <template v-if="userInfo.value && doc && userInfo.value.stu_Id === doc.uploaderId">
                      0
                    </template>
                    <template v-else-if="hasDownloaded">
                      0
                    </template>
                    <template v-else>
                      {{ doc.creditCost }}
                    </template>
                  </div>
                </div>
              </div>
            </div>

            <!-- 投诉按钮 -->
            <button 
              @click="showComplaintModal = true"
              class="complaint-button-bottom"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="complaint-icon" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
              投诉文件
            </button>
          </div>
        </template>

        <template v-else>
          <div class="no-file-state">
            <div class="no-file-icon-background">
              <svg xmlns="http://www.w3.org/2000/svg" class="no-file-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="no-file-title">该文件不存在</div>
            <p class="no-file-message">审核未通过，或者文件被删除</p>
          </div>
        </template>
      </div>
    </div>

    <!-- 下载确认模态框 (原模态框，保留) -->
    <div v-if="showModal" class="modal-backdrop">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-icon-background">
            <svg xmlns="http://www.w3.org/2000/svg" class="modal-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 class="modal-title">确认下载</h3>
          <p class="modal-message">下载此文件将消耗 <span class="modal-credit-cost">{{ doc?.creditCost }}</span> 积分，是否继续？</p>
        </div>
        <div class="modal-buttons">
          <button class="modal-confirm-button" @click="download">
            确认下载
          </button>
          <button class="modal-cancel-button" @click="showModal = false">
            取消
          </button>
        </div>
      </div>
    </div>

    <!-- 投诉模态框 (原模态框，保留) -->
    <div v-if="showComplaintModal" class="modal-backdrop">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-icon-background red-icon-bg">
            <svg xmlns="http://www.w3.org/2000/svg" class="modal-icon red-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 class="modal-title">投诉文件</h3>
          <p class="modal-message">请详细描述投诉原因，我们将认真处理您的反馈</p>
        </div>
        
        <div class="complaint-form-group">
          <label class="complaint-label">投诉原因</label>
          <textarea 
            v-model="complaintReason"
            class="complaint-textarea"
            rows="4"
            placeholder="请详细描述投诉原因..."
            maxlength="500"
          ></textarea>
          <transition name="fade">
            <p v-if="complaintReasonError" class="validation-error-message">
              {{ complaintReasonError }}
            </p>
          </transition>
          <div class="complaint-char-count">
            {{ complaintReason.length }}/500
          </div>
        </div>
        
        <div class="modal-buttons">
          <button 
            class="modal-confirm-button submit-complaint-btn" 
            @click="submitComplaint"
            :disabled="!complaintReason.trim() || submittingComplaint"
          >
            <span v-if="submittingComplaint" class="submit-loading-text">
              <div class="submit-loading-spinner"></div>
              提交中...
            </span>
            <span v-else>提交投诉</span>
          </button>
          <button 
            class="modal-cancel-button" 
            @click="closeComplaintModal"
            :disabled="submittingComplaint"
          >
            取消
          </button>
        </div>
      </div>
    </div>

    <!-- 通用反馈模态框 (取代alert) -->
    <div v-if="showFeedbackModal" class="modal-backdrop">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-icon-background" :class="feedbackIconBgClass">
            <svg v-if="feedbackType === 'success'" xmlns="http://www.w3.org/2000/svg" class="modal-icon" :class="feedbackIconClass" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <svg v-else-if="feedbackType === 'error'" xmlns="http://www.w3.org/2000/svg" class="modal-icon" :class="feedbackIconClass" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" class="modal-icon" :class="feedbackIconClass" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 class="modal-title">{{ feedbackTitle }}</h3>
          <p class="modal-message">{{ feedbackMessage }}</p>
        </div>
        <div class="modal-buttons">
          <button class="modal-confirm-button" @click="showFeedbackModal = false">
            确认
          </button>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';
// 请确保此文件存在于您的项目中并导出get和post函数
import { get, post } from '@/api/requests'; 

// 定义文档信息接口
interface DocumentInfo {
  id: string;
  fileName: string;
  description: string;
  creditCost: number;
  downLoaded: number;
  uploadedAt: string;
  uploaderName: string;
  uploaderId: string;
  size: string;
  format: string;
  text?: string; // 添加 text 字段，表示文件文本内容
}

// 定义预览接口返回的文本结构
interface PreviewResponse {
  text: string;
}

// 定义用户信息接口
interface UserInfo {
  stu_Id: string;
  token: string;
  [key: string]: any; // 允许其他属性
}

const route = useRoute();
// const router = useRouter();
const docId = computed(() => route.params.id as string); // 明确类型为字符串
const doc = ref<DocumentInfo | null>(null);
const loading = ref(false);
const showModal = ref(false); // 下载确认模态框
const showComplaintModal = ref(false); // 投诉模态框
const complaintReason = ref('');
const submittingComplaint = ref(false);
const previewContent = ref<string | null>(null); // 存储预览文本内容

// 新增：通用反馈模态框状态
const showFeedbackModal = ref(false);
const feedbackTitle = ref('');
const feedbackMessage = ref('');
const feedbackType = ref<'success' | 'error' | 'info'>('info');

// 新增：投诉原因验证错误信息
const complaintReasonError = ref('');

// 计算属性，用于动态绑定反馈模态框图标背景和图标颜色
const feedbackIconBgClass = computed(() => {
  if (feedbackType.value === 'success') return 'bg-green-50'; // Tailwind class for light green
  if (feedbackType.value === 'error') return 'bg-red-50'; // Tailwind class for light red
  return 'bg-gray-100'; // Default or info
});

const feedbackIconClass = computed(() => {
  if (feedbackType.value === 'success') return 'text-green-500'; // Tailwind class for green
  if (feedbackType.value === 'error') return 'text-red-500'; // Tailwind class for red
  return 'text-gray-500'; // Default or info
});


// 获取用户信息
const userInfo = ref<UserInfo | null>(null);
const hasDownloaded = ref(false); // 默认为false，需要根据实际API更新

// 显示通用反馈模态框
const showGenericFeedback = (type: 'success' | 'error' | 'info', title: string, message: string) => {
  feedbackType.value = type;
  feedbackTitle.value = title;
  feedbackMessage.value = message;
  showFeedbackModal.value = true;
};

// 处理下载区域点击事件：根据条件决定是直接下载还是显示确认模态框
const handleDownloadAreaClick = () => {
  if (!doc.value) return;

  // 如果是上传者或已下载，则直接触发下载
  if ((userInfo.value && doc.value && userInfo.value.stu_Id === doc.value.uploaderId) || hasDownloaded.value) {
    performDownload();
  } else {
    // 否则，显示确认模态框
    showModal.value = true;
  }
};

// 实际执行文件下载的逻辑
const performDownload = async () => {
  if (!doc.value) return;
  try {
    // 创建下载链接
    const response = await get(`/file/download/${docId.value}`, {
      responseType: 'blob' // 确保请求接收响应为blob类型
    });
    
    const url = window.URL.createObjectURL(new Blob([response as unknown as BlobPart]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', doc.value.fileName); // 设置下载文件名
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url); // 释放URL对象
    
    showModal.value = false; // 关闭下载确认模态框
    showGenericFeedback('success', '下载成功', '文件已成功下载到您的设备！');
    // 假设下载成功会更新下载状态，以便下次无需再次确认
    hasDownloaded.value = true;
    // 如果后端API返回新值，可能还需要更新doc.downLoaded字段
    // doc.value.downLoaded++; 
  } catch (error) {
    console.error('下载失败:', error);
    showGenericFeedback('error', '下载失败', '文件下载失败，请稍后重试。');
  }
};


// 下载文件（在模态框中点击“确认下载”按钮时调用）
const download = async () => {
  // 当点击“确认下载”时，执行实际的下载逻辑
  performDownload();
};

// 获取文档详情
const fetchDocumentInfo = async () => {
  try {
    loading.value = true;
    // 首先获取文档的基本信息
    const docResponse = await get(`/file/info/${docId.value}`);
    doc.value = docResponse as unknown as DocumentInfo;
    
    // 接着获取文件的预览内容
    try {
      // 假设预览接口也返回包含text字段的对象
      const previewResponse = await get(`/file/preview/${docId.value}?len=3000`);
      const previewData = previewResponse as unknown as PreviewResponse;
      if (previewData && previewData.text) {
        previewContent.value = previewData.text;
      } else {
        previewContent.value = null; // 如果没有文本内容，设为null
      }
    } catch (previewError) {
      console.error('获取文件预览失败:', previewError);
      previewContent.value = null; // 预览获取失败时设为null
    }


    // 获取用户信息
    const storedUserInfo = localStorage.getItem('userInfo');
    if (storedUserInfo) {
      try {
        const parsed = JSON.parse(storedUserInfo);
        // 确保解析结果是有效的对象，否则设为null，避免undefined
        if (parsed && typeof parsed === 'object') {
          userInfo.value = parsed;
        } else {
          userInfo.value = null; 
          console.warn('localStorage userInfo解析为null/undefined/非对象。重置为null。');
        }
      } catch (e) {
        console.error('从localStorage解析userInfo时出错:', e);
        userInfo.value = null; // 解析错误时设为null
      }
    }
    
    // 检查是否已下载（需要根据实际API实现）
    // 您需要一个实际的API调用来检查当前用户是否已下载该文件
    // 示例：hasDownloaded.value = await checkIfDownloaded(docId.value);
    // 为了演示，默认为false，除非是上传者
    if (userInfo.value && doc.value && userInfo.value.stu_Id === doc.value.uploaderId) {
        hasDownloaded.value = true;
    } else {
        hasDownloaded.value = false; // 默认为false
    }

  } catch (error) {
    console.error('获取文档信息失败:', error);
    doc.value = null; // 如果获取失败，将doc设置为null以显示“文件不存在”状态
  } finally {
    loading.value = false;
  }
};

// 格式化日期
function formatDate(dateString: string) {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
}

// 提交投诉
const submitComplaint = async () => {
  // 验证投诉原因
  if (!complaintReason.value.trim()) {
    complaintReasonError.value = '请输入投诉原因。';
    return;
  } else {
    complaintReasonError.value = ''; // 清除错误信息
  }
  
  submittingComplaint.value = true;
  try {
    await post('/audit/subAudit', {
      fileId: docId.value,
      reason: complaintReason.value.trim()
    });
    
    showGenericFeedback('success', '投诉成功', '您的投诉已成功提交，我们将尽快处理！');
    closeComplaintModal();
    // 刷新文件信息（可选，取决于投诉是否立即影响文件显示）
    await fetchDocumentInfo();
  } catch (error) {
    console.error('提交投诉失败:', error);
    showGenericFeedback('error', '投诉失败', '该文件已经提交过审核');
  } finally {
    submittingComplaint.value = false;
  }
};

// 关闭投诉模态框
const closeComplaintModal = () => {
  showComplaintModal.value = false;
  complaintReason.value = ''; // 清空投诉原因
  complaintReasonError.value = ''; // 清空错误信息
};

// 键盘事件处理程序（用于关闭模态框）
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    if (showComplaintModal.value) {
      closeComplaintModal();
    } else if (showModal.value) {
      showModal.value = false;
    } else if (showFeedbackModal.value) {
      showFeedbackModal.value = false;
    }
  }
};

// 组件挂载时执行
onMounted(() => {
  window.scrollTo({ top: 0, behavior: 'auto' });
  fetchDocumentInfo();
  document.addEventListener('keydown', handleKeydown);
});

// 组件卸载时移除键盘事件监听器
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});
</script>

<style scoped>
/* 基本样式和布局 */
.page-container {
  min-height: 100vh;
  padding: 16px; /* 调整为更适合手机的内边距 */
  background-color: #f8fafc; /* 浅色背景 */
  overflow-x: hidden; /* 防止水平滚动 */
}

@media (min-width: 640px) {
  .page-container {
    padding: 24px; /* sm:px-6 */
  }
}

@media (min-width: 1024px) {
  .page-container {
    padding: 32px; /* lg:px-8 */
  }
}

.max-w-container {
  max-width: 1024px; /* max-w-5xl */
  margin: 0 auto; /* mx-auto */
}

/* 返回按钮 */
.back-button {
  margin-bottom: 20px; /* 调整间距 */
  padding: 8px 14px; /* 调整为更适合手机的内边距 */
  border-radius: 10px; /* 调整圆角 */
  background-color: rgba(255, 255, 255, 0.9); /* bg-white/90 */
  backdrop-filter: blur(6px); /* 调整模糊效果 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.04); /* 调整阴影 */
  transition: all 0.3s ease; /* transition-all duration-300 */
  display: inline-flex; /* 确保在手机上按钮不会占据整行 */
  align-items: center;
  gap: 8px; /* 调整间距 */
  color: #4b5563; /* text-gray-700 */
  border: none;
  cursor: pointer;
  font-size: 14px; /* text-sm */
  font-weight: 500; /* font-medium */
}

.back-button:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06); /* hover:shadow-lg */
  color: #111827; /* hover:text-gray-900 */
  background-color: rgba(255, 255, 255, 0.95); /* hover:bg-white/95 */
}

.back-button .back-icon {
  width: 14px; /* 调整图标大小 */
  height: 14px;
  transition: transform 0.3s ease; /* transition-transform group-hover:-translate-x-1 */
}

.back-button:hover .back-icon {
  transform: translateX(-3px); /* 调整移动距离 */
}

/* 主要内容包装器 (简化结构，取代.main-card) */
.main-content-wrapper {
  background-color: #ffffff; /* 确保有背景色 */
  backdrop-filter: blur(8px); /* backdrop-blur-sm */
  border-radius: 20px; /* 调整圆角 */
  overflow: hidden;
  transition: all 0.5s ease; /* transform transition-all duration-500 */
  padding: 24px; /* 调整内边距 */
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05); /* 添加阴影 */
}

@media (min-width: 1024px) {
  .main-content-wrapper {
    padding: 32px; /* lg:p-8 */
  }
}


/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column; /* 更改为列方向，以便与文本更好地对齐 */
  justify-content: center;
  align-items: center;
  padding: 80px 0; /* 调整内边距 */
  gap: 12px; /* 调整间距 */
  color: #64748b; /* 添加文本颜色 */
}

.loading-spinner {
  width: 40px; /* 调整大小 */
  height: 40px; /* 调整大小 */
  border: 3px solid #e2e8f0; /* 调整边框宽度 */
  border-top-color: #10b981; /* border-t-emerald-500 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 顶部信息区域 (标题、描述、徽章) */
.top-info-section {
  margin-bottom: 24px; /* 调整间距 */
  text-align: center; /* 居中对齐以获得更好的视觉效果 */
}

.document-title-top {
  font-size: 24px; /* 更适合手机的标题大小 */
  font-weight: 800; /* 超粗体 */
  color: #1f2937; /* 深灰色 */
  margin-bottom: 8px; /* 标题下方的间距 */
  line-height: 1.3;
}

@media (min-width: 768px) {
  .document-title-top {
    font-size: 32px; /* md:text-3xl */
  }
}

.document-description-top {
  color: #4b5563; /* 中灰色 */
  font-size: 14px; /* 更适合手机的字体大小 */
  line-height: 1.5;
  white-space: pre-line; /* 保留换行符 */
  word-break: break-all; /* 单词换行 */
  margin-bottom: 16px; /* 描述下方的间距 */
  max-width: 600px; /* 限制宽度以提高可读性 */
  margin-left: auto;
  margin-right: auto;
}

.file-badges-container-top {
  flex-wrap: wrap;
  justify-content: center; /* 徽章居中 */
  gap: 4px; /* 调整间距 */
  display: flex;
}

.info-badge {
  display: flex;
  align-items: center;
  gap: 0.3em; /* 调整间距 */
  font-size: 0.9rem; /* 调整字体大小 */
  font-weight: 200;
  border-radius: 1em; /* 调整圆角 */
  padding: 0.4em 0.8em; /* 调整内边距 */
  background: #f8fafc; /* 自定义默认 */
  color: #64748b; /* 自定义默认 */
  box-shadow: 0 1px 2px rgba(0,0,0,0.04); /* 调整阴影 */
  border: 1px solid #f1f5f9; /* 自定义 */
  transition: all 0.2s ease; /* 自定义 */
}


.info-badge.user {
  background: #f0fdf4; /* bg-green-50 */
  color: #10b981; /* text-green-700 */
  border-color: #dcfce7;
}
.info-badge.download {
  background: #eff6ff; /* bg-blue-50 */
  color: #2563eb; /* text-blue-700 */
  border-color: #dbeafe;
}
.info-badge.credit {
  background: #fefce8; /* bg-yellow-50 */
  color: #eab308; /* text-yellow-700 */
  border-color: #fef3c7;
}
.info-badge.time {
  background: #faf5ff; /* bg-purple-50 */
  color: #a21caf; /* text-purple-700 */
  border-color: #f3e8ff;
}

.badge-icon {
  width: 0.9rem; /* 调整图标大小 */
  height: 0.9rem; /* 调整图标大小 */
  opacity: 0.8; /* 自定义 */
}

/* 预览和操作的章节标题 */
.section-title {
  font-size: 18px; /* 调整字体大小 */
  font-weight: 700; /* font-bold */
  color: #1f2937; /* text-gray-800 */
  margin-bottom: 12px; /* 调整间距 */
  display: flex;
  align-items: center;
  gap: 10px; /* 调整间距 */
}

.section-icon-background {
  padding: 6px; /* 调整内边距 */
  background: linear-gradient(to bottom right, #10b981, #059669); /* bg-gradient-to-br from-green-500 to-emerald-600 */
  border-radius: 10px; /* 调整圆角 */
  display: flex; /* 用于居中SVG */
  justify-content: center;
  align-items: center;
}

.section-icon {
  width: 18px; /* 调整图标大小 */
  height: 18px; /* 调整图标大小 */
  color: #ffffff; /* text-white */
}

/* 文件预览区域 */
.file-preview-section {
  background-color: #f9fafb; /* 预览的浅色背景 */
  border-radius: 16px; /* 圆角 */
  padding: 20px; /* 预览框内部的内边距 */
  margin-bottom: 24px; /* 预览下方的间距 */
  border: 1px solid #e5e7eb; /* 微妙的边框 */
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.01); /* 内部阴影以增加深度 */
}

/* 添加 .preview-content 样式 */
.preview-content {
  min-height: 250px; /* 调整最小高度 */
  max-height: 400px; /* 调整最大高度，可根据需求调整 */
  overflow-y: auto; /* 当内容超出时显示滚动条 */
  background-color: #edf2f7; /* 浅灰色背景 */
  border-radius: 10px; /* 调整圆角 */
  padding: 16px; /* 调整内边距 */
  border: 1px solid #cbd5e0; /* 边框 */
  color: #4a5568; /* 深灰色文本 */
  font-size: 14px; /* 调整字体大小 */
  line-height: 1.5;
  white-space: pre-wrap; /* 保留空白符和换行符，并自动换行 */
  word-break: break-word; /* 单词换行 */
}

.preview-text {
  /* 针对文本内容的额外样式，如果需要 */
}

.preview-placeholder {
  min-height: 250px; /* 调整高度 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: #6b7280; 
  font-size: 14px; /* 调整字体大小 */
  line-height: 1.5;
  background-color: #edf2f7; /* 浅灰色背景 */
  border-radius: 10px;
  padding: 16px;
  border: 2px dashed #cbd5e0; /* 虚线边框 */
}

/* 操作按钮区域 (下载和投诉) */
.action-buttons-section {
  display: flex;
  flex-direction: column; /* 默认垂直堆叠 */
  gap: 12px; /* 调整间距 */
}

@media (min-width: 640px) {
  .action-buttons-section {
    flex-direction: row; /* 在较大屏幕上并排 */
    align-items: stretch; /* 使项目拉伸以填充高度 */
  }
}

/* 下载区域 (现在是操作按钮部分的一部分) */
.download-area {
  background: linear-gradient(135deg, #10b981 0%, #34d399 50%, #2dd4bf 100%); /* 自定义渐变 */
  color: #ffffff; /* text-white */
  border-radius: 14px; /* 调整圆角 */
  padding: 20px; /* 调整内边距 */
  box-shadow: 0 8px 12px rgba(0, 0, 0, 0.1), 0 3px 5px rgba(0, 0, 0, 0.04); /* 调整阴影 */
  transition: all 0.5s ease; /* transition-all duration-500 transform */
  cursor: pointer;
  flex: 1; /* 允许其占用可用空间 */
  display: flex; /* 内部内容的弹性容器 */
  align-items: center;
  justify-content: space-between;
}

.download-area:hover {
  box-shadow: 0 15px 20px rgba(0, 0, 0, 0.1), 0 8px 8px rgba(0, 0, 0, 0.04); /* hover:shadow-xl */
  transform: translateY(-3px); /* hover:-translate-y-1 */
}

.download-area-content {
  display: flex;
  align-items: center;
  gap: 12px; /* 调整间距 */
  flex-grow: 1; /* 允许内容增长 */
}

.download-icon-wrapper {
  display: flex;
  align-items: center;
  gap: 12px; /* 调整间距 */
}

.download-icon-background {
  padding: 10px; /* 调整内边距 */
  background-color: rgba(255, 255, 255, 0.2); /* bg-white/20 */
  backdrop-filter: blur(6px); /* 调整模糊效果 */
  border-radius: 10px; /* 调整圆角 */
  transition: all 0.3s ease; /* transition-all duration-300 */
  display: flex; /* 用于居中SVG */
  justify-content: center;
  align-items: center;
}

.download-area:hover .download-icon-background {
  background-color: rgba(255, 255, 255, 0.3); /* group-hover:bg-white/30 */
}

.download-icon {
  width: 20px; /* 调整图标大小 */
  height: 20px; /* 调整图标大小 */
  color: #ffffff; /* 从父级继承的白色 */
}

.download-status-text {
  font-size: 13px; /* 调整字体大小 */
  line-height: 18px;
  color: rgba(255, 255, 255, 0.9); /* text-white/90 */
  margin-bottom: 2px; /* 调整间距 */
  font-weight: 500;
}

.download-file-name {
  font-weight: 700; /* font-bold */
  font-size: 16px; /* 调整字体大小 */
  line-height: 24px;
}

.download-credit-info {
  text-align: right;
  flex-shrink: 0; /* 防止收缩 */
}

.download-credit-label {
  font-size: 13px; /* 调整字体大小 */
  line-height: 18px;
  color: rgba(255, 255, 255, 0.9); /* text-white/90 */
  margin-bottom: 2px; /* 调整间距 */
  font-weight: 500;
}

.download-credit-cost {
  font-weight: 700; /* font-bold */
  font-size: 20px; /* 调整字体大小 */
  line-height: 28px;
}

/* 投诉按钮 (现在是一个独立的按钮) */
.complaint-button-bottom {
  padding: 10px 20px; /* 调整内边距 */
  border-radius: 10px; /* 调整圆角 */
  transition: all 0.3s ease; /* transition-all duration-300 */
  display: flex;
  align-items: center;
  justify-content: center; /* 内容居中 */
  gap: 6px; /* 调整间距 */
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.04); /* 调整阴影 */
  font-size: 15px; /* 调整字体大小 */
  line-height: 22px; /* leading-normal */
  background-color: #dc2626; /* bg-red-600 */
  color: #ffffff; /* text-white */
  border: none;
  cursor: pointer;
  flex: 0 0 auto; /* 不增长，不收缩 */
}

.complaint-button-bottom:hover {
  background-color: #b91c1c; /* hover:bg-red-700 */
}

.complaint-button-bottom .complaint-icon {
  width: 18px; /* 调整图标大小 */
  height: 18px; /* 调整图标大小 */
}


/* 文件未找到状态 */
.no-file-state {
  text-align: center;
  padding: 80px 0; /* 调整内边距 */
}

.no-file-icon-background {
  width: 80px; /* 调整大小 */
  height: 80px; /* 调整大小 */
  background: linear-gradient(to bottom right, #dcfce7, #dcfce7); /* bg-gradient-to-br from-green-100 to-emerald-100 */
  border-radius: 50%; /* rounded-full */
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px auto; /* 调整间距 */
}

.no-file-icon {
  width: 40px; /* 调整大小 */
  height: 40px; /* 调整大小 */
  color: #4ade80; /* text-green-400 */
}

.no-file-title {
  color: #6b7280; /* text-gray-500 */
  font-size: 20px; /* 调整字体大小 */
  font-weight: 500; /* font-medium */
}

.no-file-message {
  color: #9ca3af; /* text-gray-400 */
  margin-top: 6px; /* 调整间距 */
  font-size: 13px; /* 调整字体大小 */
}

/* 模态框背景 */
.modal-backdrop {
  position: fixed;
  inset: 0; /* inset-0 */
  z-index: 50; /* z-50 */
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.6); /* 调整背景透明度 */
  backdrop-filter: blur(6px); /* 调整模糊效果 */
}

/* 模态框内容 */
.modal-content {
  background-color: #ffffff; /* bg-white */
  border-radius: 14px; /* 调整圆角 */
  box-shadow: 0 8px 12px rgba(0, 0, 0, 0.15), 0 3px 6px rgba(0, 0, 0, 0.08); /* 调整阴影 */
  padding: 20px; /* 调整内边距 */
  width: 90vw; /* w-[90vw] */
  max-width: 350px; /* 调整最大宽度 */
  margin: 0 auto; /* mx-auto */
  transform: scale(1); /* scale-100 */
  transition: all 0.3s ease; /* transition-all duration-300 */
}

.modal-header {
  text-align: center;
  margin-bottom: 20px; /* 调整间距 */
}

.modal-icon-background {
  width: 50px; /* 调整大小 */
  height: 50px; /* 调整大小 */
  background-color: #f0fdf4; /* bg-green-50 */
  border-radius: 50%; /* rounded-full */
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 10px auto; /* 调整间距 */
}

.modal-icon {
  width: 25px; /* 调整大小 */
  height: 25px; /* 调整大小 */
  color: #10b981; /* text-green-500 */
}

.modal-icon-background.red-icon-bg {
  background-color: #fee2e2; /* bg-red-50 */
}

.modal-icon.red-icon {
  color: #ef4444; /* text-red-500 */
}

.modal-title {
  font-size: 17px; /* 调整字体大小 */
  font-weight: 700; /* font-bold */
  color: #1f2937; /* text-gray-800 */
  margin-bottom: 6px; /* 调整间距 */
}

.modal-message {
  color: #4b5563; /* text-gray-600 */
  font-size: 13px; /* 调整字体大小 */
  line-height: 18px;
}

.modal-credit-cost {
  color: #10b981; /* text-green-500 */
  font-weight: 700; /* font-bold */
  font-size: 15px; /* 调整字体大小 */
}

.modal-buttons {
  display: flex;
  gap: 10px; /* 调整间距 */
}

.modal-confirm-button {
  flex: 1; /* flex-1 */
  padding: 10px 0; /* 调整内边距 */
  border-radius: 10px; /* 调整圆角 */
  background: linear-gradient(to right, #10b981, #059669); /* bg-gradient-to-r from-green-500 to-emerald-500 */
  color: #ffffff; /* text-white */
  font-weight: 500;
  transition: all 0.3s ease; /* transition-all duration-300 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.04); /* 调整阴影 */
  font-size: 14px; /* text-sm */
  line-height: 20px;
  border: none;
  cursor: pointer;
}

.modal-confirm-button:hover {
  background: linear-gradient(to right, #0e9e6e, #047857); /* hover:from-green-600 hover:to-emerald-600 */
  box-shadow: 0 5px 8px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.05); /* hover:shadow-lg */
  transform: translateY(-1px); /* hover:-translate-y-0.5 */
}

.modal-confirm-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.modal-cancel-button {
  flex: 1; /* flex-1 */
  padding: 10px 0; /* py-3 */
  border-radius: 10px; /* 调整圆角 */
  background-color: #f3f4f6; /* bg-gray-100 */
  color: #4b5563; /* text-gray-600 */
  font-weight: 500;
  transition: all 0.3s ease; /* transition-all duration-300 */
  font-size: 14px; /* text-sm */
  line-height: 20px;
  border: none;
  cursor: pointer;
}

.modal-cancel-button:hover {
  background-color: #e5e7eb; /* hover:bg-gray-200 */
}

/* 投诉表单 */
.complaint-form-group {
  margin-bottom: 20px; /* 调整间距 */
}

.complaint-label {
  display: block;
  font-size: 13px; /* 调整字体大小 */
  font-weight: 500; /* font-medium */
  color: #374151; /* text-gray-700 */
  margin-bottom: 6px; /* 调整间距 */
}

.complaint-textarea {
  width: 100%; /* w-full */
  padding: 10px 14px; /* 调整内边距 */
  border: 1px solid #d1d5db; /* border border-gray-300 */
  border-radius: 10px; /* 调整圆角 */
  resize: none; /* resize-none */
  font-size: 14px; /* text-sm */
  line-height: 20px;
  transition: all 0.3s ease; /* transition-all duration-300 */
}

.complaint-textarea:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.5); /* focus:ring-2 focus:ring-red-500 */
  border-color: #ef4444; /* focus:border-red-500 */
}

/* 新增：验证错误消息样式 */
.validation-error-message {
  color: #ef4444; /* text-red-500 */
  font-size: 12px; /* text-xs */
  margin-top: 4px; /* mt-1 */
}

.complaint-char-count {
  text-align: right;
  font-size: 11px; /* 调整字体大小 */
  line-height: 14px;
  color: #6b7280; /* text-gray-500 */
  margin-top: 6px; /* 调整间距 */
}

.submit-complaint-btn {
  background-color: #dc2626; /* bg-red-600 */
  color: #ffffff;
  border: none;
  box-shadow: 0 2px 6px rgba(220, 38, 38, 0.12); /* 调整阴影 */
}

.submit-loading-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px; /* 调整间距 */
}

.submit-loading-spinner {
  width: 14px; /* 调整大小 */
  height: 14px; /* 调整大小 */
  border: 2px solid #ffffff; /* border-2 border-white */
  border-top-color: transparent; /* border-t-transparent */
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Vue 过渡类 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

/* 额外样式和主题颜色 */
body {
  background-color: #f8fafc; /* Tailwind: bg-slate-50 */
}

.info-badge.user .badge-icon,
.modal-icon {
  color: #10b981; /* text-green-500, text-emerald-500, text-emerald-600 */
}

.modal-credit-cost {
  color: #10b981;
}

/* 投诉相关颜色 */
.complaint-button-bottom,
.submit-complaint-btn {
  background-color: #dc2626; /* bg-red-600 */
}
.complaint-button-bottom:hover,
.submit-complaint-btn:hover {
  background-color: #b91c1c; /* hover:bg-red-700 */
}

/* 通用反馈模态框图标颜色 */
.modal-icon.text-green-500 {
  color: #10b981;
}

.modal-icon.text-red-500 {
  color: #ef4444;
}

/* 通用反馈模态框背景颜色 */
.modal-icon-background.bg-green-50 {
  background-color: #f0fdf4;
}

.modal-icon-background.bg-red-50 {
  background-color: #fee2e2;
}

.complaint-textarea:focus {
  border-color: #ef4444; /* focus:border-red-500 */
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.5); /* focus:ring-red-500 */
}
</style>
