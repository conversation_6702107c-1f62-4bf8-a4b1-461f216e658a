<template>
  <div class="min-h-screen bg-white py-4 px-4">
    <div class="max-w-3xl mx-auto">
      <!-- 顶部导航 -->
      <div class="flex items-center gap-3 mb-6">
        <button @click="goBack" class="p-2 rounded-full bg-white/80 backdrop-blur-sm shadow-sm hover:bg-white transition-all">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-600" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
          </svg>
        </button>
        <h1 class="text-xl font-bold text-red-800">我被投诉的文件</h1>
      </div>

      <!-- 内容区域 -->
      <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg p-4">
        <div v-if="loading" class="flex flex-col items-center justify-center py-12">
          <div class="w-8 h-8 border-4 border-red-200 border-t-red-500 rounded-full animate-spin mb-3"></div>
          <span class="text-sm text-gray-500">加载中...</span>
        </div>

        <div v-else-if="complaints.length" class="space-y-3">
          <div v-for="complaint in complaints" :key="complaint.auditId" 
               class="bg-white rounded-xl p-4 shadow-sm border border-red-100 hover:shadow-md transition-all cursor-pointer hover:bg-red-50/50 group"
               @click="viewFile(complaint.fileId)">
            <div class="flex flex-col gap-2">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <h3 class="font-semibold text-gray-900 text-base group-hover:text-red-700 transition-colors">文件ID: {{ complaint.fileId }}</h3>
                  <div class="flex items-center gap-2 mt-1">
                    <span class="px-2 py-0.5 rounded-full bg-red-50 text-red-600 text-xs">被投诉</span>
                    <span class="text-xs text-gray-500">{{ formatDate(complaint.subTime) }}</span>
                  </div>
                </div>
                <div class="flex items-center gap-2">
                  <div class="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <button @click.stop="viewFile(complaint.fileId)" 
                          class="px-3 py-1.5 rounded-lg bg-red-50 text-red-600 hover:bg-red-100 transition-colors text-xs font-medium">
                    查看详情
                  </button>
                </div>
              </div>
              
              <div class="flex items-center gap-1 text-xs text-gray-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-orange-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                </svg>
                <span>投诉ID: {{ complaint.auditId }}</span>
              </div>

              <!-- 投诉原因优化 -->
              <div class="flex items-start gap-2 mt-2">
                <span class="inline-flex items-center px-2 py-0.5 rounded bg-red-50 text-red-600 text-xs font-semibold">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0z" />
                  </svg>
                  投诉原因
                </span>
                <span class="text-xs text-gray-700 font-medium break-all">{{ complaint.reason }}</span>
              </div>

              <!-- 状态标签 -->
              <div class="flex justify-end mt-3 pt-2 border-t border-gray-100">
                <span 
                  class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium"
                  :class="complaint.isHandled 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-orange-100 text-orange-800'"
                >
                  <svg 
                    class="w-3 h-3 mr-1" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path 
                      v-if="complaint.isHandled"
                      stroke-linecap="round" 
                      stroke-linejoin="round" 
                      stroke-width="2" 
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" 
                    />
                    <path 
                      v-else
                      stroke-linecap="round" 
                      stroke-linejoin="round" 
                      stroke-width="2" 
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" 
                    />
                  </svg>
                  {{ complaint.isHandled ? '已处理' : '待处理' }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="flex flex-col items-center justify-center py-12 text-gray-500">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-300 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <span class="text-sm">暂无投诉记录</span>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="total > pageSize" class="mt-6">
        <div class="pagination">
          <button @click="prevPage" :disabled="page <= 1" class="pagination-button prev">
            <svg xmlns="http://www.w3.org/2000/svg" class="pagination-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            上一页
          </button>
          
          <button v-for="p in displayedPages" :key="p" @click="goToPage(p)" 
            :class="['pagination-button', 'page-number', p === page ? 'active' : '', p === -1 ? 'ellipsis' : '']" 
            :disabled="p === -1">
            <span v-if="p !== -1">{{ p }}</span>
            <span v-else class="ellipsis-text">...</span>
          </button>
          
          <button @click="nextPage" :disabled="page >= totalPages" class="pagination-button next">
            下一页
            <svg xmlns="http://www.w3.org/2000/svg" class="pagination-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
// @ts-ignore
import { get, post } from '../api/requests'

const router = useRouter()

// 定义投诉数据类型
interface Complaint {
  auditId: string
  reason: string
  fileId: string
  subTime: string
  isHandled: boolean
}

interface ComplaintResponse {
  total: number
  page: number
  pageSize: number
  data: Complaint[]
}

// 数据状态
const loading = ref(false)
const complaints = ref<Complaint[]>([])
const total = ref(0)
const page = ref(1)
const pageSize = ref(5)

// 计算属性
// const pendingCount = computed(() => {
//   return complaints.value.filter(c => !c.isHandled).length
// })

// const handledCount = computed(() => {
//   return complaints.value.filter(c => c.isHandled).length
// })

// 分页计算属性
const totalPages = computed(() => {
  const pages = Math.ceil(total.value / pageSize.value)
  return Math.max(pages, 1) // 至少返回1页
})

const displayedPages = computed(() => {
  const pages: number[] = []
  const currentPage = page.value
  const total = totalPages.value
  
  if (total <= 7) {
    // 如果总页数小于等于7，显示所有页码
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // 如果总页数大于7，显示部分页码
    // 始终显示第一页
    pages.push(1)
    
    if (currentPage <= 4) {
      // 当前页在前4页，显示1-5页
      for (let i = 2; i <= 5; i++) {
        pages.push(i)
      }
      pages.push(-1) // 省略号
      pages.push(total)
    } else if (currentPage >= total - 3) {
      // 当前页在后4页，显示最后5页
      pages.push(-1) // 省略号
      for (let i = total - 4; i <= total; i++) {
        pages.push(i)
      }
    } else {
      // 当前页在中间，显示当前页前后各2页
      pages.push(-1) // 省略号
      for (let i = currentPage - 2; i <= currentPage + 2; i++) {
        pages.push(i)
      }
      pages.push(-1) // 省略号
      pages.push(total)
    }
  }
  
  return pages
})

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取投诉列表
const fetchComplaints = async () => {
  loading.value = true
  try {
    const response = await get(`/audit/getAuditHistory?type=0&page=${page.value}&pageSize=${pageSize.value}`) as unknown as ComplaintResponse
    if (response) {
      complaints.value = response.data || []
      total.value = response.total || 0
    }
  } catch (error) {
    console.error('获取投诉列表失败:', error)
    // 使用模拟数据
    complaints.value = [
    ]
    total.value = 3
  } finally {
    loading.value = false
  }
}

// 查看文件
const viewFile = (fileId: string) => {
  router.push(`/document/${fileId}`)
}

// 处理投诉
// const handleComplaint = async (auditId: string) => {
//   try {
//     await post(`/audit/handleComplaint`, { auditId })
//     // 重新获取列表
//     await fetchComplaints()
//     alert('投诉已标记为已处理')
//   } catch (error) {
//     console.error('处理投诉失败:', error)
//     alert('处理失败，请重试')
//   }
// }

// 切换页面
// const changePage = (newPage: number) => {
//   page.value = newPage
//   fetchComplaints()
// }

// 返回上一级
const goBack = () => {
  router.back()
}

// 跳转到指定页
const goToPage = (pageNum: number) => {
  if (pageNum !== -1) {
    page.value = pageNum
    fetchComplaints()
  }
}

// 上一页
const prevPage = () => {
  if (page.value > 1) {
    page.value--
    fetchComplaints()
  }
}

// 下一页
const nextPage = () => {
  if (page.value < totalPages.value) {
    page.value++
    fetchComplaints()
  }
}

// 组件挂载时获取数据
onMounted(() => {
  // 滚动到页面顶部
  window.scrollTo({ top: 0, behavior: 'auto' })
  fetchComplaints()
})
</script>

<style scoped>
/* 可以添加自定义样式 */

/* 分页器样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-top: 2rem;
  flex-wrap: wrap;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 1rem;
  padding: 1rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.pagination-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border: 2px solid rgba(239, 68, 68, 0.2);
  border-radius: 0.75rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.3s ease;
  min-width: 2.5rem;
  height: 2.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.pagination-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(239, 68, 68, 0.1), transparent);
  transition: left 0.5s ease;
}

.pagination-button:hover:not(:disabled)::before {
  left: 100%;
}

.pagination-button.active {
  background: rgb(239, 68, 68);
  border-color: rgb(239, 68, 68);
  color: white;
  font-weight: 700;
  box-shadow: 0 4px 16px rgba(239, 68, 68, 0.4);
  transform: translateY(-1px);
}

.pagination-button.active::before {
  display: none;
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.pagination-button.ellipsis {
  background: transparent;
  border-color: transparent;
  color: #94a3b8;
  font-weight: normal;
  cursor: default;
  box-shadow: none;
  min-width: 2rem;
}

.pagination-button.ellipsis:hover {
  background: transparent;
  border-color: transparent;
  transform: none;
  box-shadow: none;
}

.ellipsis-text {
  font-size: 1.2rem;
  font-weight: bold;
  color: #94a3b8;
}

.pagination-icon {
  width: 1rem;
  height: 1rem;
  transition: transform 0.3s ease;
}

.pagination-button:hover:not(:disabled) .pagination-icon {
  transform: scale(1.1);
}

.pagination-button.prev {
  padding-left: 1rem;
  padding-right: 1.25rem;
}

.pagination-button.next {
  padding-left: 1.25rem;
  padding-right: 1rem;
}

.page-number {
  min-width: 2.5rem;
  padding: 0.75rem 0.5rem;
}

/* 响应式分页器 */
@media (max-width: 768px) {
  .pagination {
    gap: 0.25rem;
    padding: 0.75rem;
  }
  
  .pagination-button {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    min-width: 2rem;
    height: 2rem;
  }
  
  .pagination-button.prev,
  .pagination-button.next {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }
  
  .page-number {
    min-width: 2rem;
    padding: 0.5rem 0.25rem;
  }
  
  .pagination-icon {
    width: 0.875rem;
    height: 0.875rem;
  }
}

@media (max-width: 480px) {
  .pagination {
    gap: 0.25rem;
    padding: 0.5rem;
  }
  
  .pagination-button {
    padding: 0.5rem 0.5rem;
    font-size: 0.75rem;
    min-width: 1.75rem;
    height: 1.75rem;
  }
  
  .pagination-button.prev,
  .pagination-button.next {
    padding: 0.5rem 0.75rem;
    font-size: 0.7rem;
  }
  
  .page-number {
    min-width: 1.75rem;
    padding: 0.5rem 0.25rem;
  }
}
</style>