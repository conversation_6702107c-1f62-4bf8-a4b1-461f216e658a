<template>
  <nav class="fixed bottom-0 left-0 right-0 bg-white border-t shadow z-50 flex justify-around items-center h-14">
    <router-link to="/" class="flex flex-col items-center flex-1 py-1" active-class="text-blue-600 router-link-exact-active">
      <img src="@/img/ziliao.png" alt="资料" class="h-6 w-6" />
      <span class="text-xs mt-0.5">资料</span>
    </router-link>
    <router-link to="/upload" class="flex flex-col items-center flex-1 py-1" active-class="text-blue-600 router-link-exact-active">
      <img src="@/img/post.png" alt="上传" class="h-6 w-6" />
      <span class="text-xs mt-0.5">上传</span>
    </router-link>
    <router-link to="/profile" class="flex flex-col items-center flex-1 py-1" active-class="text-blue-600 router-link-exact-active">
      <img src="@/img/my.png" alt="我的" class="h-6 w-6" />
      <span class="text-xs mt-0.5">我的</span>
    </router-link>

    <router-link v-if="isAdmin" to="/admin-review" class="flex flex-col items-center flex-1 py-1" active-class="text-blue-600 router-link-exact-active">
      <img src="@/img/admin-review.png" alt="试卷审核" class="h-6 w-6" />
      <span class="text-xs mt-0.5">试卷审核</span>
    </router-link>
  </nav>
</template>

<script setup>
import { computed } from 'vue'

const getCurrentUser = () => {
  const userInfoStr = localStorage.getItem('userInfo')
  if (userInfoStr) {
    try {
      return JSON.parse(userInfoStr)
    } catch (e) {
      console.error('Error parsing user info:', e)
      return null
    }
  }
  return null
}

const isAdmin = computed(() => {
  const user = getCurrentUser()
  return user && (user.role === 'Admin' || user.role === 'Auditor')
})
</script>

<style scoped>
nav {
  box-shadow: 0 -2px 8px rgba(0,0,0,0.04);
}
.router-link-exact-active {
  color: #25bb0e !important;
  font-weight: bold;
}
</style>