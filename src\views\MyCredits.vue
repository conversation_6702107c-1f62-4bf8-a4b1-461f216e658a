<template>
  <div class="credit-log">
    <h2>积分明细</h2>
    <table>
      <thead>
        <tr>
          <th>时间</th>
          <th>积分变动</th>
          <th>来源</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="item in logs" :key="item.id">
          <td>{{ formatTime(item.addTime) }}</td>
          <td :style="{color: item.amount > 0 ? 'green' : 'red'}">
            {{ item.amount > 0 ? '+' : '' }}{{ item.amount }}
          </td>
          <td>{{ item.source }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { get } from '@/api/requests'

const logs = ref([])

onMounted(async () => {
  logs.value = await get('/getInfo/getCreditLog')
})

function formatTime(time) {
  return new Date(time).toLocaleString()
}
</script>