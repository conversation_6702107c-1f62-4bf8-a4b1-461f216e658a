#!/bin/bash

# 数据共享平台部署脚本
# 使用方法: ./deploy.sh

set -e  # 遇到错误立即退出

echo "🚀 开始部署数据共享平台..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="data-sharing-platform"
BUILD_DIR="dist"
DEPLOY_DIR="/www/wwwroot/117.72.215.94"
BACKUP_DIR="/www/wwwroot/backup"
NGINX_CONF="/etc/nginx/sites-available/117.72.215.94.conf"
NGINX_SITES_ENABLED="/etc/nginx/sites-enabled/117.72.215.94.conf"

# 检查Node.js和npm
check_dependencies() {
    echo "📋 检查依赖..."
    
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 未安装${NC}"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm 未安装${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 依赖检查完成${NC}"
}

# 安装依赖
install_dependencies() {
    echo "📦 安装项目依赖..."
    
    if [ ! -d "node_modules" ]; then
        npm install
    else
        npm install --prefer-offline
    fi
    
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
}

# 构建项目
build_project() {
    echo "🔨 构建项目..."
    
    # 清理之前的构建
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
    fi
    
    # 构建项目
    npm run build
    
    if [ ! -d "$BUILD_DIR" ]; then
        echo -e "${RED}❌ 构建失败，dist目录不存在${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 项目构建完成${NC}"
}

# 备份当前部署
backup_current() {
    echo "💾 备份当前部署..."
    
    if [ -d "$DEPLOY_DIR" ]; then
        # 创建备份目录
        mkdir -p "$BACKUP_DIR"
        
        # 创建带时间戳的备份
        BACKUP_NAME="backup_$(date +%Y%m%d_%H%M%S)"
        cp -r "$DEPLOY_DIR" "$BACKUP_DIR/$BACKUP_NAME"
        
        echo -e "${GREEN}✅ 备份完成: $BACKUP_DIR/$BACKUP_NAME${NC}"
    else
        echo -e "${YELLOW}⚠️  没有找到现有部署，跳过备份${NC}"
    fi
}

# 部署到nginx目录
deploy_to_nginx() {
    echo "📤 部署到nginx目录..."
    
    # 创建部署目录
    mkdir -p "$DEPLOY_DIR"
    
    # 复制构建文件
    cp -r "$BUILD_DIR"/* "$DEPLOY_DIR/"
    
    # 设置权限
    chown -R www-data:www-data "$DEPLOY_DIR"
    chmod -R 755 "$DEPLOY_DIR"
    
    echo -e "${GREEN}✅ 部署完成${NC}"
}

# 配置nginx
configure_nginx() {
    echo "⚙️  配置nginx..."
    
    # 检查nginx配置语法
    if nginx -t; then
        echo -e "${GREEN}✅ nginx配置语法正确${NC}"
    else
        echo -e "${RED}❌ nginx配置语法错误${NC}"
        exit 1
    fi
    
    # 重新加载nginx
    systemctl reload nginx
    
    echo -e "${GREEN}✅ nginx配置完成${NC}"
}

# 清理旧备份
cleanup_old_backups() {
    echo "🧹 清理旧备份..."
    
    if [ -d "$BACKUP_DIR" ]; then
        # 保留最近5个备份
        cd "$BACKUP_DIR"
        ls -t | tail -n +6 | xargs -r rm -rf
        echo -e "${GREEN}✅ 旧备份清理完成${NC}"
    fi
}

# 健康检查
health_check() {
    echo "🏥 执行健康检查..."
    
    # 等待nginx重启
    sleep 2
    
    # 检查网站是否可访问
    if curl -f -s http://117.72.215.94 > /dev/null; then
        echo -e "${GREEN}✅ 网站健康检查通过${NC}"
    else
        echo -e "${YELLOW}⚠️  网站可能还在启动中，请稍后手动检查${NC}"
    fi
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo -e "${GREEN}🎉 部署完成！${NC}"
    echo "=================================="
    echo "🌐 网站地址: http://117.72.215.94"
    echo "📁 部署目录: $DEPLOY_DIR"
    echo "📁 备份目录: $BACKUP_DIR"
    echo "📋 nginx配置: $NGINX_CONF"
    echo "=================================="
    echo ""
    echo "📝 常用命令:"
    echo "  - 查看nginx状态: systemctl status nginx"
    echo "  - 重启nginx: systemctl restart nginx"
    echo "  - 查看日志: tail -f /www/wwwlogs/117.72.215.94.log"
    echo "  - 查看错误日志: tail -f /www/wwwlogs/117.72.215.94.error.log"
    echo ""
}

# 主函数
main() {
    echo -e "${GREEN}================================${NC}"
    echo -e "${GREEN}   数据共享平台部署脚本${NC}"
    echo -e "${GREEN}================================${NC}"
    echo ""
    
    check_dependencies
    install_dependencies
    build_project
    backup_current
    deploy_to_nginx
    configure_nginx
    cleanup_old_backups
    health_check
    show_deployment_info
}

# 执行主函数
main "$@" 