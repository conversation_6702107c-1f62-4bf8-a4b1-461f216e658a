{"name": "doc-share-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@heroicons/vue": "^2.1.1", "axios": "^1.7.2", "element-plus": "^2.7.5", "pinia": "^2.1.7", "vue": "^3.4.31", "vue-router": "^4.3.3"}, "devDependencies": {"@types/node": "^20.14.8", "@vitejs/plugin-vue": "^5.0.5", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "typescript": "^5.5.2", "vite": "^5.3.1", "vue-tsc": "^2.0.22"}}