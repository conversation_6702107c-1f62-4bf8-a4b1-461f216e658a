<template>
  <div class="min-h-screen bg-white py-4 px-4">
    <div class="max-w-3xl mx-auto">
      <!-- 顶部导航 -->
      <div class="flex items-center gap-3 mb-6">
        <button @click="$router.back()" class="p-2 rounded-full bg-white/80 backdrop-blur-sm shadow-sm hover:bg-white transition-all">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
          </svg>
        </button>
        <h1 class="text-xl font-bold text-green-800">我的上传</h1>
      </div>

      <!-- 内容区域 -->
      
        <div v-if="loading" class="flex flex-col items-center justify-center py-12">
          <div class="w-8 h-8 border-4 border-green-200 border-t-green-500 rounded-full animate-spin mb-3"></div>
          <span class="text-sm text-gray-500">加载中...</span>
        </div>

        <div v-else-if="uploads.length" class="space-y-3">
          <div v-for="item in uploads" :key="item.id || item.fileId" 
               class="bg-white rounded-xl p-4 shadow-sm border border-green-100 hover:shadow-md transition-all cursor-pointer hover:bg-green-50/50 group"
               @click="goToDetail(item.id || item.fileId)">
            <div class="flex flex-col gap-2">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <h3 class="font-semibold text-gray-900 text-base group-hover:text-green-700 transition-colors">{{ item.fileName }}</h3>
                  <div class="flex items-center gap-2 mt-1">
                    <span class="px-2 py-0.5 rounded-full bg-green-50 text-green-600 text-xs">已上传</span>
                    <span class="text-xs text-gray-500">{{ formatDate(item.uploadedAt) }}</span>
                  </div>
                </div>
                <div class="flex items-center gap-2">
                  <div class="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
           
                  </div>
                  <div class="flex flex-col gap-1">
                    <button @click.stop="handleDelete(item.id || item.fileId)" 
                            class="p-1.5 rounded-lg bg-red-50 text-red-500 hover:bg-red-100 transition-colors z-10">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                  </svg>
                </button>
             
                  </div>
                </div>
              </div>
              
              <div class="grid grid-cols-2 gap-2 text-xs text-gray-600">
                <div class="flex items-center gap-1">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                  <span>下载次数：{{ item.downLoaded }}</span>
                </div>
                <div class="flex items-center gap-1">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd" />
                  </svg>
                  <span>所需积分：{{ item.creditCost }}</span>
                </div>
              </div>

              <div class="text-xs text-gray-500 mt-1 line-clamp-2">
                {{ item.description }}
              </div>
              
              <!-- 操作按钮区域 -->
              <div class="flex justify-end mt-3 pt-2 border-t border-gray-100">
                <button @click.stop="goToDetail(item.id || item.fileId)" 
                        class="px-3 py-1.5 rounded-lg bg-green-50 text-green-600 hover:bg-green-100 transition-colors text-xs font-medium">
                  查看详情
                </button>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="flex flex-col items-center justify-center py-12 text-gray-500">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-300 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>
          <span class="text-sm">暂无上传记录</span>
        </div>
    </div>

    <!-- 顶部弹窗提示 -->
    <div v-if="showMessage" :class="['fixed top-6 left-1/2 z-50 px-6 py-3 rounded-lg shadow-lg text-white text-base font-bold transition-all duration-300', messageType === 'success' ? 'bg-green-500' : 'bg-red-500']" style="transform: translateX(-50%);">
      {{ message }}
    </div>

    <!-- 删除确认对话框 -->
    <div v-if="showDeleteModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
      <div class="bg-white rounded-lg shadow-xl p-5 w-[85vw] max-w-sm mx-auto transform transition-all duration-300 scale-100">
        <div class="text-center mb-4">
          <div class="w-12 h-12 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 class="text-base font-bold text-gray-800 mb-1">确认删除</h3>
          <p class="text-gray-600 text-sm">确定要删除这个文件吗？此操作不可撤销。</p>
        </div>
        <div class="flex gap-2">
          <button 
            class="flex-1 py-2 rounded-md bg-red-500 text-white font-medium hover:bg-red-600 transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 text-sm" 
            @click="confirmDelete"
          >
            确认删除
          </button>
          <button 
            class="flex-1 py-2 rounded-md bg-gray-100 text-gray-600 font-medium hover:bg-gray-200 transition-all duration-300 text-sm" 
            @click="showDeleteModal = false"
          >
            取消
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { get, post } from '@/api/requests'

const router = useRouter()
const uploads = ref<any[]>([])
const loading = ref(true)

// 顶部弹窗提示
const message = ref('')
const messageType = ref<'success'|'error'>('success')
const showMessage = ref(false)
function showTip(msg: string, type: 'success'|'error' = 'success') {
  message.value = msg
  messageType.value = type
  showMessage.value = true
  setTimeout(() => { showMessage.value = false }, 2000)
}

// 删除确认对话框
const showDeleteModal = ref(false)
const currentFileIdToDelete = ref<number | null>(null)

const confirmDelete = async () => {
  if (currentFileIdToDelete.value === null) return

  try {
    loading.value = true
    const response = await post(`/file/delete/${currentFileIdToDelete.value}`)
    console.log('删除响应:', response)
    showTip('删除成功', 'success')
    // 重新获取列表
    await fetchUploads()
  } catch (e) {
    console.error('删除失败:', e)
    showTip('删除失败，请重试', 'error')
  } finally {
    loading.value = false
    showDeleteModal.value = false
    currentFileIdToDelete.value = null
  }
}

// 获取上传列表
const fetchUploads = async () => {
  try {
    const res = await get('/getInfo/getUpLoadLog')
    console.log('接口返回：', res)
    if (res.data) {
      uploads.value = Array.isArray(res.data) ? res.data : []
      console.log('处理后的数据：', uploads.value)
      // 打印每个项目的结构
      uploads.value.forEach((item, index) => {
        console.log(`项目 ${index} 的结构:`, item)
      })
    } else {
      console.error('接口返回数据格式不正确：', res)
      uploads.value = []
    }
  } catch (e) {
    console.error('获取上传列表失败:', e)
    uploads.value = []
  } finally {
    loading.value = false
  }
}

// 删除文件
const handleDelete = (id: number) => {
  console.log('点击删除按钮，文件ID:', id)
  currentFileIdToDelete.value = id
  showDeleteModal.value = true
}

// 跳转到文件详情页
function goToDetail(fileId: number) {
  if (fileId) {
    router.push(`/document/${fileId}`)
  }
}

onMounted(() => {
  // 滚动到页面顶部
  window.scrollTo({ top: 0, behavior: 'auto' })
  fetchUploads()
})

function formatDate(dateStr: string) {
  if (!dateStr) return ''
  const d = new Date(dateStr)
  return d.toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' })
}
</script> 