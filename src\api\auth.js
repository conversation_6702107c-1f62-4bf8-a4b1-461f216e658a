import axios from 'axios'
import baseURL from './baseurl'

// 创建axios实例
const authApi = axios.create({
  baseURL: '/api',
  timeout: 5000
})

// 处理授权请求
export const handleAuth = async (code) => {
  try {
    const response = await authApi.get(`/auth?code=${code}`)
    if (response.data) {
      // 将用户信息和token存储到localStorage
      localStorage.setItem('userInfo', JSON.stringify({
        stu_Name: response.data.stu_Name,
        stu_Nickname: response.data.stu_Nickname,
        stu_Avator: response.data.stu_Avator,
        stu_Id: response.data.stu_Id,
        stu_section: response.data.stu_section,
        role: response.data.role,
        token: response.data.token
      }))
      return response.data
    }
  } catch (error) {
    console.error('Auth request failed:', error)
    throw error
  }
}

// 获取本地存储的用户信息
export const getUserInfo = () => {
  const userInfo = localStorage.getItem('userInfo')
  return userInfo ? JSON.parse(userInfo) : null
}

// 获取token
export const getToken = () => {
  const userInfo = getUserInfo()
  return userInfo ? userInfo.token : null
}



// 检查是否已登录
export const isLoggedIn = () => {
  return !!getUserInfo()
}

