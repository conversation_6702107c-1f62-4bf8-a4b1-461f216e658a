<template>
  <div class="min-h-screen-custom bg-gray-50-custom py-4-custom px-4-custom sm:px-6-custom lg:px-8-custom font-sans-custom">
    <div class="max-w-3xl-custom mx-auto-custom space-y-6-custom">
      <!-- User Info Section -->
      <div class="user-info-card-custom">
        <div class="user-info-card-pattern-custom"></div>
        <img :src="userInfo.stu_Avator || 'https://placehold.co/150x150/10B981/FFFFFF?text=Avatar'" alt="头像" class="avatar-custom" />
        <div class="user-nickname-custom">{{ userInfo.stu_Nickname || userInfo.stu_Name || '未登录' }}</div>
        <div class="user-detail-text-custom">学号：{{ userInfo.stu_Id || '未知' }}</div>
        <div class="user-detail-text-custom">部门：{{ userInfo.stu_section || '未知' }}</div>
        <div class="user-detail-text-custom mt-1-custom">
          角色：<span class="role-text-custom">{{ roleDisplayText }}</span>
        </div>
        <div class="user-detail-text-custom mt-2-custom">当前积分：<span class="current-credit-text-custom">{{ currentCredit }}</span></div>
        <div class="user-info-card-bottom-gradient-custom"></div>
      </div>

      <!-- Feature Entry Section -->
      <div class="feature-links-container-custom">
        <router-link to="/my-downloads" class="feature-link-custom group">
          <div class="feature-icon-wrapper-custom bg-blue-100-custom text-blue-500-custom">
            <svg class="w-6-h-6-custom sm:w-7-h-7-custom" viewBox="0 0 1028 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
              <path d="M793.6 780.8c-12.8 0-25.6-12.8-25.6-25.6s12.8-25.6 25.6-25.6c102.4 0 183.466667-81.066667 183.466667-183.466667 0-93.866667-72.533333-174.933333-170.666667-183.466666-12.8 0-21.333333-8.533333-25.6-21.333334-25.6-110.933333-140.8-192-273.066667-192-128 0-238.933333 76.8-268.8 187.733334 0 8.533333-8.533333 17.066667-21.333333 17.066666-89.6 12.8-162.133333 89.6-162.133333 183.466667 0 102.4 81.066667 183.466667 183.466666 183.466667 12.8 0 25.6 12.8 25.6 25.6s-12.8 25.6-25.6 25.6c-132.266667 0-238.933333-106.666667-238.933333-238.933334 4.266667-106.666667 85.333333-204.8 192-226.133333 42.666667-123.733333 170.666667-209.066667 315.733333-209.066667 145.066667 0 277.333333 89.6 320 213.333334C942.933333 328.533333 1028.266667 426.666667 1028.266667 541.866667c0 132.266667-106.666667 238.933333-234.666667 238.933333z" fill="currentColor"/>
              <path d="M516.266667 925.866667c-8.533333 0-12.8-4.266667-17.066667-8.533334l-166.4-166.4c-8.533333-8.533333-8.533333-25.6 0-38.4 8.533333-8.533333 25.6-8.533333 38.4 0l149.333333 149.333334 149.333334-149.333334c8.533333-8.533333 25.6-8.533333 38.4 0 8.533333 8.533333 8.533333 25.6 0 38.4l-166.4 166.4c-12.8 8.533333-17.066667 8.533333-25.6 8.533334z" fill="currentColor"/>
              <path d="M516.266667 913.066667c-12.8 0-25.6-12.8-25.6-25.6v-354.133334c0-12.8 12.8-25.6 25.6-25.6s25.6 12.8 25.6 25.6v354.133334c0 12.8-12.8 25.6-25.6 25.6z" fill="currentColor"/>
            </svg>
          </div>
          <div class="flex-1-custom">
            <h3 class="feature-title-custom">我的下载</h3>
            <p class="feature-description-custom">查看下载历史</p>
          </div>
          <svg xmlns="http://www.w3.org/2000/svg" class="feature-arrow-custom" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
          </svg>
        </router-link>

        <router-link to="/my-uploads" class="feature-link-custom group">
          <div class="feature-icon-wrapper-custom bg-blue-100-custom text-blue-500-custom">
            <svg class="w-6-h-6-custom sm:w-7-h-7-custom" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
              <path d="M789.333333 768c-12.8 0-25.6-12.8-25.6-25.6s12.8-25.6 25.6-25.6c102.4 0 183.466667-81.066667 183.466667-183.466667 0-93.866667-72.533333-174.933333-170.666667-183.466666-12.8 0-21.333333-8.533333-25.6-21.333334-25.6-110.933333-140.8-192-273.066666-192-128 0-238.933333 76.8-268.8 187.733334 0 12.8-8.533333 21.333333-21.333334 21.333333-89.6 12.8-162.133333 89.6-162.133333 183.466667 0 102.4 81.066667 183.466667 183.466667 183.466666 12.8 0 25.6 12.8 25.6 25.6s-8.533333 29.866667-21.333334 29.866667C106.666667 768 0 661.333333 0 529.066667 0 418.133333 81.066667 320 187.733333 298.666667c42.666667-128 170.666667-213.333333 320-213.333334 145.066667 0 277.333333 89.6 320 213.333334 110.933333 21.333333 196.266667 119.466667 196.266667 234.666666 0 132.266667-106.666667 234.666667-234.666667 234.666667z" fill="currentColor"/>
              <path d="M678.4 738.133333c-8.533333 0-12.8-4.266667-17.066667-8.533333L512 580.266667l-149.333333 149.333333c-8.533333 8.533333-25.6 8.533333-38.4 0s-8.533333-25.6 0-38.4l166.4-166.4c8.533333-8.533333 29.866667-8.533333 38.4 0l166.4 166.4c8.533333 8.533333 8.533333 25.6 0 38.4 0 4.266667-8.533333 8.533333-17.066667 8.533333z" fill="currentColor"/>
              <path d="M512 938.666667c-12.8 0-25.6-12.8-25.6-25.6v-354.133334c0-12.8 12.8-25.6 25.6-25.6s25.6 12.8 25.6 25.6v354.133334c0 12.8-12.8 25.6-25.6 25.6z" fill="currentColor"/>
            </svg>
          </div>
          <div class="flex-1-custom">
            <h3 class="feature-title-custom">我的上传</h3>
            <p class="feature-description-custom">管理上传的资料</p>
          </div>
          <svg xmlns="http://www.w3.org/2000/svg" class="feature-arrow-custom" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
        </router-link>

        <!-- Only show "我被投诉" if user is Admin or Auditor -->
        <router-link v-if="isAdmin" to="/my-review" class="feature-link-custom group">
          <div class="feature-icon-wrapper-custom bg-red-100-custom text-red-500-custom">
            <svg class="w-6-h-6-custom sm:w-7-h-7-custom" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <div class="flex-1-custom">
            <h3 class="feature-title-custom">我被投诉</h3>
            <p class="feature-description-custom">查看被投诉的文件</p>
          </div>
          <svg xmlns="http://www.w3.org/2000/svg" class="feature-arrow-custom" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
        </router-link>

        <router-link to="/points-detail" class="feature-link-custom group">
          <div class="feature-icon-wrapper-custom bg-yellow-100-custom text-yellow-500-custom">
            <svg class="w-6-h-6-custom sm:w-7-h-7-custom" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
              <path d="M423.7312 355.5328c114.8928 0 234.3424-25.2416 306.0736-73.4208v87.6544h66.4576v-192c0-115.3536-191.8464-177.664-372.3776-177.664S51.3536 62.4128 51.3536 177.7664v523.9808c0 115.456 191.8976 177.7664 372.3776 177.7664v-66.3552c-189.5936 0-306.0224-64.9216-306.0224-111.4112V544.256c71.7312 48.0256 191.0784 73.4208 306.0224 73.4208v-66.4576C234.1376 551.2192 117.76 486.3488 117.76 439.808V282.2144c71.7312 48.0768 191.1808 73.3184 306.0224 73.3184z m0-289.0752c189.6448 0 306.0736 64.8704 306.0736 111.36 0 46.4896-116.4288 111.36-306.0736 111.36-189.5936 0-306.0224-64.8704-306.0224-111.36 0-46.4896 116.5312-111.36 306.0224-111.36z m297.7792 325.4272c-164.9152 0-251.1872 63.488-251.1872 126.1056v336.6912c0 62.6688 86.3744 126.0544 251.1872 126.0544 163.6864 0 249.8048-62.5152 251.136-124.7232h0.1024v-338.0224c-0.1024-62.6688-86.3232-126.1056-251.2384-126.1056z m0 66.3552c121.9072 0 184.7808 41.8816 184.7808 59.7504 0 17.8688-62.8736 59.6992-184.832 59.6992-121.856 0-184.7808-41.8304-184.7808-59.6992s62.8736-59.7504 184.832-59.7504z m0 456.192c-121.9584 0-184.832-41.8816-184.832-59.7504v-80.384c42.1376 22.8864 103.936 38.144 184.832 38.144 80.7936 0 142.5408-15.2576 184.7808-38.144v80.384c0 17.8176-62.8736 59.6992-184.832 59.6992z m0-168.448c-121.9584 0-184.832-41.8816-184.832-59.6992v-80.384c42.1376 22.8864 103.936 38.144 184.832 38.144 80.7936 0 142.5408-15.2576 184.7808-38.144v80.384c0 17.8176-62.8736 59.6992-184.832 59.6992z" fill="currentColor"/>
            </svg>
          </div>
          <div class="flex-1-custom">
            <h3 class="feature-title-custom">积分明细</h3>
            <p class="feature-description-custom">查看积分明细</p>
          </div>
          <svg xmlns="http://www.w3.org/2000/svg" class="feature-arrow-custom" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
          </svg>
        </router-link>

        <router-link to="/points-rules" class="feature-link-custom group">
          <div class="feature-icon-wrapper-custom bg-yellow-100-custom text-yellow-500-custom">
            <svg class="w-6-h-6-custom sm:w-7-h-7-custom" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
              <path d="M112.12673 284.089243a390.766 200.815 0 1 0 799.74654 0 390.766 200.815 0 1 0-799.74654 0ZM512 551.408545c-163.335019 0-303.791981-50.327222-365.869828-122.45189-21.856797 25.394367-34.003442 53.489238-34.003442 83.043345 0 113.491821 179.029466 205.495234 399.87327 205.495234s399.87327-92.003414 399.87327-205.495234c0-29.554106-12.145621-57.648978-34.003442-83.043345C815.791981 501.081323 675.335019 551.408545 512 551.408545zM512 784.985348c-165.466566 0-307.456441-51.64831-368.263341-125.285424-20.35049 24.644283-31.609928 51.751664-31.609928 80.20981 0 113.491821 179.029466 205.495234 399.87327 205.495234s399.87327-92.003414 399.87327-205.495234c0-28.459169-11.259438-55.565527-31.609928-80.20981C819.456441 733.337037 677.466566 784.985348 512 784.985348z" fill="currentColor"/>
            </svg>
          </div>
          <div class="flex-1-custom">
            <h3 class="feature-title-custom">积分规则</h3>
            <p class="feature-description-custom">了解如何获取和使用积分</p>
          </div>
          <svg xmlns="http://www.w3.org/2000/svg" class="feature-arrow-custom" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
          </svg>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { get } from '@/api/requests'

interface UserInfo {
  stu_Name: string
  stu_Id: string
  stu_section: string
  stu_Avator?: string
  stu_Nickname?: string
  role?: string
  token?: string
  credit?: number // Added credit to UserInfo interface for consistency
}

const route = useRoute()
const userInfo = ref<UserInfo>({
  stu_Name: '',
  stu_Id: '',
  stu_section: '',
  stu_Avator: '',
  stu_Nickname: '',
  role: '',
  token: '',
  credit: 0 // Initialize credit
})
const currentCredit = ref(0) 

// Determine if user is Admin
const isAdmin = computed(() => {
  // This can be adjusted based on actual user role/permission logic
  const userInfoStr = localStorage.getItem('userInfo')
  if (userInfoStr) {
    try {
      const user = JSON.parse(userInfoStr)
      // Adjust admin check logic as needed (e.g., check user ID, role field, etc.)
      return user.role === 'Admin' || user.role === 'Auditor' || user.isAdmin === true
    } catch (e) {
      console.error('Error parsing user info from localStorage:', e);
      return false
    }
  }
  return false
})

// Load user info from localStorage
const loadUserInfo = () => {
  const storedUserInfo = localStorage.getItem('userInfo')
  if (storedUserInfo) {
    try {
      const parsedInfo = JSON.parse(storedUserInfo)
      // Keep existing data, only update new fetched data
      userInfo.value = {
        ...userInfo.value,
        stu_Name: parsedInfo.stu_Name || userInfo.value.stu_Name,
        stu_Id: parsedInfo.stu_Id || userInfo.value.stu_Id,
        stu_section: parsedInfo.stu_section || userInfo.value.stu_section,
        stu_Avator: parsedInfo.stu_Avator || userInfo.value.stu_Avator,
        stu_Nickname: parsedInfo.stu_Nickname || userInfo.value.stu_Nickname,
        role: parsedInfo.role || userInfo.value.role,
        token: parsedInfo.token || userInfo.value.token,
        credit: parsedInfo.credit || userInfo.value.credit // Ensure credit is loaded here
      }
      currentCredit.value = userInfo.value.credit || 0; // Initialize currentCredit from loaded userInfo
    } catch (e: unknown) {
      console.error('Error parsing user info:', e)
      // No longer display error messages to avoid bothering the user
    }
  }
}

// Watch for route changes
watch(() => route.fullPath, () => {
  loadUserInfo()
})

// On component mount, load user info and latest credit
onMounted(async () => {
  window.scrollTo({ top: 0, behavior: 'auto' })
  loadUserInfo() // Loads initial userInfo and credit from localStorage

  // Request latest credit
  try {
    const res = await get('/getInfo/sign')
    if (res && typeof res === 'object' && 'nowCredit' in res && typeof res.nowCredit === 'number') {
      currentCredit.value = res.nowCredit; // Update reactive ref for display
      // Update userInfo object and save to localStorage for persistence
      if (userInfo.value) {
        userInfo.value.credit = res.nowCredit;
        localStorage.setItem('userInfo', JSON.stringify(userInfo.value));
      }
    }
  } catch (e) {
    // Ignore credit API exceptions
    console.warn('Failed to fetch user credit:', e);
  }
})

const roleDisplayText = computed(() => {
  if (userInfo.value.role === 'Admin') {
    return '管理员'
  } else if (userInfo.value.role === 'Auditor') {
    return '审核员'
  } else {
    return '普通用户'
  }
})
</script>

<style scoped>
/* Base Styles */
.min-h-screen-custom {
  min-height: 100vh;
}
.bg-gray-50-custom {
  background-color: #f9fafb;
}
.py-4-custom {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.px-4-custom {
  padding-left: 1rem;
  padding-right: 1rem;
}
.font-sans-custom {
  font-family: 'Inter', sans-serif;
}
.max-w-3xl-custom {
  max-width: 48rem; /* Equivalent to max-w-3xl */
}
.mx-auto-custom {
  margin-left: auto;
  margin-right: auto;
}
.space-y-6-custom > *:not([hidden]) ~ *:not([hidden]) {
  margin-top: 1.5rem; /* Equivalent to space-y-6 */
}
.space-y-4-custom > *:not([hidden]) ~ *:not([hidden]) {
  margin-top: 1rem; /* Equivalent to space-y-4 */
}
.mt-1-custom {
  margin-top: 0.25rem;
}
.mt-2-custom {
  margin-top: 0.5rem;
}
.flex-1-custom {
  flex: 1 1 0%;
}
.w-6-h-6-custom {
  width: 1.5rem; /* 24px */
  height: 1.5rem; /* 24px */
}
.sm\:w-7-h-7-custom {
  width: 1.75rem; /* 28px */
  height: 1.75rem; /* 28px */
}

/* Responsive adjustments */
@media (min-width: 640px) { /* sm breakpoint */
  .sm\:px-6-custom {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}
@media (min-width: 1024px) { /* lg breakpoint */
  .lg\:px-8-custom {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}


/* User Info Card Custom Styles */
.user-info-card-custom {
  background-image: linear-gradient(to right, #10b981, #059669); /* from-emerald-500 to-green-600 */
  border-radius: 1.5rem; /* rounded-3xl */
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); /* shadow-xl */
  padding: 1.5rem; /* p-6 */
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #ffffff; /* text-white */
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out; /* transition-all duration-300 */
}
.user-info-card-custom:hover {
  transform: scale(1.01); /* hover:scale-[1.01] */
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); /* hover:shadow-2xl */
}

/* User Info Card Background Pattern */
.user-info-card-pattern-custom {
  position: absolute;
  inset: 0;
  background-image: url('data:image/svg+xml,%3Csvg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%23ffffff" fill-opacity="0.2"%3E%3Cpath d="M96 95h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zm-9-13h4v1h-4zm-9 13h4v1h-4zM0 0h4v1h-4zM9 0h4v1h-4zM18 0h4v1h-4zM27 0h4v1h-4zM36 0h4v1h-4zM45 0h4v1h-4zM54 0h4v1h-4zM63 0h4v1h-4zM72 0h4v1h-4zM81 0h4v1h-4zM90 0h4v1h-4zM99 0h4v1h-4zM0 9h4v1h-4zM9 9h4v1h-4zM18 9h4v1h-4zM27 9h4v1h-4zM36 9h4v1h-4zM45 9h4v1h-4zM54 9h4v1h-4zM63 9h4v1h-4zM72 9h4v1h-4zM81 9h4v1h-4zM90 9h4v1h-4zM99 9h4v1h-4zM0 18h4v1h-4zM9 18h4v1h-4zM18 18h4v1h-4zM27 18h4v1h-4zM36 18h4v1h-4zM45 18h4v1h-4zM54 18h4v1h-4zM63 18h4v1h-4zM72 18h4v1h-4zM81 18h4v1h-4zM90 18h4v1h-4zM99 18h4v1h-4zM0 27h4v1h-4zM9 27h4v1h-4zM18 27h4v1h-4zM27 27h4v1h-4zM36 27h4v1h-4zM45 27h4v1h-4zM54 27h4v1h-4zM63 27h4v1h-4zM72 27h4v1h-4zM81 27h4v1h-4zM90 27h4v1h-4zM99 27h4v1h-4zM0 36h4v1h-4zM9 36h4v1h-4zM18 36h4v1h-4zM27 36h4v1h-4zM36 36h4v1h-4zM45 36h4v1h-4zM54 36h4v1h-4zM63 36h4v1h-4zM72 36h4v1h-4zM81 36h4v1h-4zM90 36h4v1h-4zM99 36h4v1h-4zM0 45h4v1h-4zM9 45h4v1h-4zM18 45h4v1h-4zM27 45h4v1h-4zM36 45h4v1h-4zM45 45h4v1h-4zM54 45h4v1h-4zM63 45h4v1h-4zM72 45h4v1h-4zM81 45h4v1h-4zM90 45h4v1h-4zM99 45h4v1h-4zM0 54h4v1h-4zM9 54h4v1h-4zM18 54h4v1h-4zM27 54h4v1h-4zM36 54h4v1h-4zM45 54h4v1h-4zM54 54h4v1h-4zM63 54h4v1h-4zM72 54h4v1h-4zM81 54h4v1h-4zM90 54h4v1h-4zM99 54h4v1h-4zM0 63h4v1h-4zM9 63h4v1h-4zM18 63h4v1h-4zM27 63h4v1h-4zM36 63h4v1h-4zM45 63h4v1h-4zM54 63h4v1h-4zM63 63h4v1h-4zM72 63h4v1h-4zM81 63h4v1h-4zM90 63h4v1h-4zM99 63h4v1h-4zM0 72h4v1h-4zM9 72h4v1h-4zM18 72h4v1h-4zM27 72h4v1h-4zM36 72h4v1h-4zM45 72h4v1h-4zM54 72h4v1h-4zM63 72h4v1h-4zM72 72h4v1h-4zM81 72h4v1h-4zM90 72h4v1h-4zM99 72h4v1h-4zM0 81h4v1h-4zM9 81h4v1h-4zM18 81h4v1h-4zM27 81h4v1h-4zM36 81h4v1h-4zM45 81h4v1h-4zM54 81h4v1h-4zM63 81h4v1h-4zM72 81h4v1h-4zM81 81h4v1h-4zM90 81h4v1h-4zM99 81h4v1h-4zM0 90h4v1h-4zM9 90h4v1h-4zM18 90h4v1h-4zM27 90h4v1h-4zM36 90h4v1h-4zM45 90h4v1h-4zM54 90h4v1h-4zM63 90h4v1h-4zM72 90h4v1h-4zM81 90h4v1h-4zM90 90h4v1h-4zM99 90h4v1h-4z"/%3E%3C/g%3E%3C/svg%3E');
  opacity: 0.1;
  z-index: 0;
}

.avatar-custom {
  width: 5rem; /* w-20 */
  height: 5rem; /* h-20 */
  border-radius: 9999px; /* rounded-full */
  background-color: #ffffff; /* bg-white */
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); /* shadow-lg */
  border: 4px solid #ffffff; /* border-4 border-white */
  margin-top: -2.5rem; /* -mt-10 */
  margin-bottom: 0.75rem; /* mb-3 */
  margin-left: auto;
  margin-right: auto;
  flex-shrink: 0;
  object-fit: cover;
  transition: transform 0.3s ease-in-out; /* transition-transform duration-300 */
}
.avatar-custom:hover {
  transform: rotate(3deg); /* hover:rotate-3 */
}

.user-nickname-custom {
  font-size: 1.25rem; /* text-xl */
  font-weight: 800; /* font-extrabold */
  margin-bottom: 0.25rem; /* mb-1 */
  text-align: center;
  width: 100%;
  position: relative;
  z-index: 10;
  filter: drop-shadow(0 1px 1px rgba(0,0,0,0.05)); /* drop-shadow-sm */
}

.user-detail-text-custom {
  font-size: 0.875rem; /* text-sm */
  opacity: 0.9; /* opacity-90 */
  text-align: center;
  width: 100%;
  position: relative;
  z-index: 10;
  filter: drop-shadow(0 1px 1px rgba(0,0,0,0.05)); /* drop-shadow-sm */
}

.role-text-custom {
  color: #fcd34d; /* text-yellow-300 */
  font-weight: 700; /* font-bold */
}

.current-credit-text-custom {
  color: #fcd34d; /* text-yellow-300 */
  font-weight: 700; /* font-bold */
  font-size: 1.125rem; /* text-lg */
}

.user-info-card-bottom-gradient-custom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 33.333333%; /* h-1/3 */
  background-color: #ffffff;
  opacity: 0.1; /* bg-opacity-10 */
  z-index: 0;
}

/* Feature Link Common Styles */
.feature-links-container-custom {
  display: flex;
  flex-direction: column;
}

.feature-link-custom {
  background-color: #ffffff; /* bg-white */
  border-radius: 1rem; /* rounded-2xl */
  padding: 1rem; /* p-4 */
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); /* shadow-sm */
  border: 1px solid #e5e7eb; /* border border-gray-200 */
  display: flex;
  align-items: center;
  gap: 0.75rem; /* gap-3 */
  transition: all 0.3s ease-in-out; /* transition-all duration-300 */
  transform: translateY(0); /* initial transform for hover */
}
.feature-link-custom:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); /* hover:shadow-md */
  border-color: #a7f3d0; /* hover:border-emerald-200 */
  transform: translateY(-0.125rem); /* hover:-translate-y-0.5 */
}

.feature-icon-wrapper-custom {
  width: 3rem; /* w-12 */
  height: 3rem; /* h-12 */
  border-radius: 9999px; /* rounded-full */
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: transform 0.3s ease-in-out; /* transition-transform duration-300 */
}
.feature-link-custom:hover .feature-icon-wrapper-custom { /* group-hover:scale-110 group-hover:rotate-6 */
  transform: scale(1.1) rotate(6deg);
}

.bg-blue-100-custom {
  background-color: #dbeafe;
}
.text-blue-500-custom {
  color: #3b82f6;
}
.bg-red-100-custom {
  background-color: #fee2e2;
}
.text-red-500-custom {
  color: #ef4444;
}
.bg-yellow-100-custom {
  background-color: #fef9c3;
}
.text-yellow-500-custom {
  color: #f59e0b;
}

.feature-title-custom {
  font-weight: 600; /* font-semibold */
  color: #1f2937; /* text-gray-900 */
  font-size: 1.125rem; /* text-lg */
}

.feature-description-custom {
  font-size: 0.875rem; /* text-sm */
  color: #6b7280; /* text-gray-500 */
}

.feature-arrow-custom {
  height: 1.25rem; /* h-5 */
  width: 1.25rem; /* w-5 */
  color: #9ca3af; /* text-gray-400 */
  transition: transform 0.2s ease-in-out; /* transition-transform duration-200 */
}
.feature-link-custom:hover .feature-arrow-custom { /* group-hover:translate-x-1 */
  transform: translateX(0.25rem);
}

/* Responsive adjustments for sm breakpoint */
@media (min-width: 640px) {
  .user-info-card-custom {
    padding: 2rem; /* sm:p-8 */
  }
  .avatar-custom {
    width: 6rem; /* sm:w-24 */
    height: 6rem; /* sm:h-24 */
    margin-top: -3rem; /* sm:-mt-12 */
  }
  .user-nickname-custom {
    font-size: 1.5rem; /* sm:text-2xl */
  }
  .user-detail-text-custom {
    font-size: 1rem; /* sm:text-base */
  }
  .current-credit-text-custom {
    font-size: 1.25rem; /* sm:text-xl */
  }
  .feature-link-custom {
    padding: 1.25rem; /* sm:p-5 */
    gap: 1rem; /* sm:gap-4 */
  }
  .feature-icon-wrapper-custom {
    width: 3.5rem; /* sm:w-14 */
    height: 3.5rem; /* sm:h-14 */
  }
  .feature-title-custom {
    font-size: 1.25rem; /* sm:text-xl */
  }
  .feature-description-custom {
    font-size: 1rem; /* sm:text-base */
  }
  .feature-arrow-custom {
    height: 1.5rem; /* sm:h-6 */
    width: 1.5rem; /* sm:w-6 */
  }
}

/* Router Link Active State (optional, if you want a specific active style) */
/* This would require specific JavaScript logic to apply an active class based on Vue Router's active link state */
/* For example, you might add a class like '.router-link-active-custom' when the link is active */
/*
.router-link-active-custom {
  border-color: #10b981;
  color: #047857;
}
*/
</style>
