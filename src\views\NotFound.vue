<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50">
    <div class="max-w-md w-full text-center space-y-8">
      <div class="space-y-4">
        <h1 class="text-9xl font-bold text-primary">404</h1>
        <h2 class="text-2xl font-medium text-gray-900">页面未找到</h2>
        <p class="text-gray-600">
          抱歉，您访问的页面不存在或已被移除。
        </p>
      </div>
      <div class="space-y-4">
        <router-link
          to="/"
          class="btn btn-primary w-full"
        >
          返回首页
        </router-link>
        <button
          class="btn btn-outline w-full"
          @click="goBack"
        >
          返回上一页
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goBack = () => {
  router.back()
}
</script> 