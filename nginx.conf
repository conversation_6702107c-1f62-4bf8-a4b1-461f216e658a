 server {
    listen 80;
    server_name *************;
    root /www/wwwroot/*************;
    index index.html index.htm default.htm default.html;

    # SSL-START SSL配置(请勿删除此注释)
    # SSL-END(请勿删除此注释)
    
    # PROXY-START(请勿删除此注释)
    include /xp/panel/vhost/proxy/*************/*.conf;
    # PROXY-END(请勿删除此注释)

    # ERROR-PAGE-START 错误页配置
    # ERROR-PAGE-END

    # REWRITE-START 伪静态配置
    # include /xp/panel/vhost/rewrite/*************.conf;
    # REWRITE-END

    # =========================
    # 安全头配置
    # =========================
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # =========================
    # 反向代理 API 请求
    # =========================
    
    # /api 代理，去掉 /api 前缀
    location /api/ {
        proxy_pass http://*************:5000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
    }

    # /auth 代理
    location /auth/ {
        proxy_pass http://*************:5000/auth/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # /documents 代理
    location /documents/ {
        proxy_pass http://*************:5000/documents/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # /upload 代理
    location /upload/ {
        proxy_pass http://*************:5000/upload/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 文件上传大小限制
        client_max_body_size 100M;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # =========================
    # Vue Router SPA 配置
    # =========================
    location / {
        try_files $uri $uri/ /index.html;
        
        # 静态文件缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;
        }
    }

    # =========================
    # 静态资源缓存优化
    # =========================
    
    # 缓存多媒体文件
    location ~* \.(gif|jpg|jpeg|png|bmp|swf|webp|avif)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # 缓存js和css文件
    location ~* \.(js|css)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # 缓存字体文件
    location ~* \.(woff|woff2|ttf|eot|otf)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # =========================
    # 安全配置
    # =========================

    # 禁止访问的文件或目录
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md|package\.json|package-lock\.json|vite\.config\.ts|tsconfig\.json|tailwind\.config\.js|postcss\.config\.js) {
        return 404;
    }

    # 一键申请SSL证书验证目录相关设置
    location ~ \.well-known {
        allow all;
    }

    # 禁止在证书验证目录放入敏感文件
    if ($uri ~ "^/\.well-known/.*\.(php|jsp|py|js|css|lua|ts|go|zip|tar\.gz|rar|7z|sql|bak)$") {
        return 403;
    }

    # 禁止访问源码文件
    location ~ \.(vue|ts|js|map)$ {
        location ~ ^/(src|node_modules)/ {
            return 404;
        }
    }

    # =========================
    # 日志配置
    # =========================
    access_log  /www/wwwlogs/*************.log;
    error_log   /www/wwwlogs/*************.error.log;
} 