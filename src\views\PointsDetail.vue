<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 py-4 px-4">
    <div class="max-w-3xl mx-auto">
      <!-- 顶部导航 -->
      <div class="flex items-center gap-3 mb-6">
        <button @click="$router.back()" class="p-2 rounded-full bg-white/80 backdrop-blur-sm shadow-sm hover:bg-white transition-all">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
          </svg>
        </button>
        <h1 class="text-xl font-bold text-blue-800">积分明细</h1>
      </div>

      <!-- 内容区域 -->
      
        <div v-if="loading" class="flex flex-col items-center justify-center py-12">
          <div class="w-8 h-8 border-4 border-blue-200 border-t-blue-500 rounded-full animate-spin mb-3"></div>
          <span class="text-sm text-gray-500">加载中...</span>
        </div>

        <div v-else-if="credits.length" class="space-y-3">
          <div v-for="item in credits" :key="item.id" 
               class="bg-white rounded-xl p-4 shadow-sm border border-blue-100 hover:shadow-md transition-all">
            <div class="flex flex-col gap-2">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <h3 class="font-semibold text-gray-900 text-base">{{ item.source }}</h3>
                  <div class="flex items-center gap-2 mt-1">
                    <span :class="[
                      'px-2 py-0.5 rounded-full text-xs',
                      item.amount > 0 
                        ? 'bg-green-50 text-green-600' 
                        : 'bg-red-50 text-red-600'
                    ]">
                      {{ item.amount > 0 ? '积分增加' : '积分减少' }}
                    </span>
                    <span class="text-xs text-gray-500">{{ formatDate(item.addTime) }}</span>
                  </div>
                </div>
                <div :class="[
                  'text-lg font-semibold',
                  item.amount > 0 ? 'text-green-600' : 'text-red-600'
                ]">
                  {{ item.amount > 0 ? '+' : '' }}{{ item.amount }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="flex flex-col items-center justify-center py-12 text-gray-500">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-300 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span class="text-sm">暂无积分记录</span>
        </div>
      
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { get } from '@/api/requests'

const credits = ref<any[]>([])
const loading = ref(true)

const formatDate = (date: string) => {
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const fetchCredits = async () => {
  try {
    const res = await get('/getInfo/getCreditLog')
    console.log('接口返回内容:', res)
    credits.value = Array.isArray(res) ? res : []
  } catch (e) {
    credits.value = []
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchCredits()
})
</script> 