#!/bin/bash

# nginx配置快速修复脚本
# 解决 proxy_busy_buffers_size 错误

echo "🔧 修复nginx配置..."

# 备份当前配置
if [ -f "/etc/nginx/sites-available/117.72.215.94.conf" ]; then
    sudo cp /etc/nginx/sites-available/117.72.215.94.conf /etc/nginx/sites-available/117.72.215.94.conf.backup
    echo "✅ 已备份当前配置"
fi

# 使用简化配置
if [ -f "nginx-simple.conf" ]; then
    sudo cp nginx-simple.conf /etc/nginx/sites-available/117.72.215.94.conf
    echo "✅ 已应用简化配置"
else
    echo "❌ 找不到 nginx-simple.conf 文件"
    exit 1
fi

# 测试配置
echo "🧪 测试nginx配置..."
if sudo nginx -t; then
    echo "✅ nginx配置测试通过"
    
    # 重新加载nginx
    echo "🔄 重新加载nginx..."
    sudo systemctl reload nginx
    
    if [ $? -eq 0 ]; then
        echo "✅ nginx重新加载成功"
        echo "🌐 网站应该可以正常访问了"
    else
        echo "❌ nginx重新加载失败"
        echo "请检查nginx状态: sudo systemctl status nginx"
    fi
else
    echo "❌ nginx配置测试失败"
    echo "请检查配置文件语法"
fi 