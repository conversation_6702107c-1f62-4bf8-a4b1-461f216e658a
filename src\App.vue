<template>
  <div class="pb-14"> <!-- 防止内容被底部导航遮住 -->
    <router-view />
  </div>
  <NavBar v-if="$route.path !== '/login'" />
</template>

<script setup>
import NavBar from './components/NavBar.vue'
</script>

<style>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 为底部导航栏留出空间 */
.router-view {
  padding-bottom: 4rem;
}

html, body, #app {
  height: 100%;
  margin: 0;
  padding: 0;
}
</style> 