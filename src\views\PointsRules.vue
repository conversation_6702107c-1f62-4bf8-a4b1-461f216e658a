<template>
  <div class="min-h-screen bg-white py-4 px-4">
    <div class="max-w-3xl mx-auto">
      <!-- 顶部导航 -->
      <div class="flex items-center gap-3 mb-6">
        <button @click="$router.back()" class="p-2 rounded-full bg-white/80 backdrop-blur-sm shadow-sm hover:bg-white transition-all">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
          </svg>
        </button>
        <h1 class="text-xl font-bold text-yellow-800">积分规则</h1>
      </div>

      <!-- 内容区域 -->
      
        <div class="space-y-4">
          <!-- 规则列表 -->
          <div class="space-y-3">
            <div class="bg-white rounded-xl p-4 shadow-sm border border-yellow-100 hover:shadow-md transition-all">
              <div class="flex items-center gap-3">
                <div class="p-2 bg-green-50 rounded-lg">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="flex-1">
                  <h3 class="font-semibold text-gray-900">注册账号</h3>
                  <p class="text-sm text-gray-600">新用户注册即可获得积分奖励</p>
                </div>
                <span class="text-green-600 font-bold text-lg">+100</span>
              </div>
            </div>

            <div class="bg-white rounded-xl p-4 shadow-sm border border-yellow-100 hover:shadow-md transition-all">
              <div class="flex items-center gap-3">
                <div class="p-2 bg-green-50 rounded-lg">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="flex-1">
                  <h3 class="font-semibold text-gray-900">上传资料通过审核</h3>
                  <p class="text-sm text-gray-600">分享优质资料，获得积分奖励</p>
                </div>
                <span class="text-green-600 font-bold text-lg">+10</span>
              </div>
            </div>

            <div class="bg-white rounded-xl p-4 shadow-sm border border-yellow-100 hover:shadow-md transition-all">
              <div class="flex items-center gap-3">
                <div class="p-2 bg-green-50 rounded-lg">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="flex-1">
                  <h3 class="font-semibold text-gray-900">资料被下载</h3>
                  <p class="text-sm text-gray-600">您的资料被其他用户下载</p>
                </div>
                <span class="text-green-600 font-bold text-lg">+1</span>
              </div>
            </div>

            <div class="bg-white rounded-xl p-4 shadow-sm border border-yellow-100 hover:shadow-md transition-all">
              <div class="flex items-center gap-3">
                <div class="p-2 bg-red-50 rounded-lg">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-600" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="flex-1">
                  <h3 class="font-semibold text-gray-900">下载普通资料</h3>
                  <p class="text-sm text-gray-600">下载平台上的普通资料</p>
                </div>
                <span class="text-red-500 font-bold text-lg">-5</span>
              </div>
            </div>

            <div class="bg-white rounded-xl p-4 shadow-sm border border-yellow-100 hover:shadow-md transition-all">
              <div class="flex items-center gap-3">
                <div class="p-2 bg-red-50 rounded-lg">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-600" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="flex-1">
                  <h3 class="font-semibold text-gray-900">下载热门资料</h3>
                  <p class="text-sm text-gray-600">下载下载量≥100的热门资料</p>
                </div>
                <span class="text-red-500 font-bold text-lg">-10</span>
              </div>
            </div>
          </div>

          <!-- 说明文字 -->
          <div class="mt-6 p-4 bg-yellow-50 rounded-xl border border-yellow-200">
            <div class="flex items-start gap-3">
              <div class="p-1.5 bg-yellow-100 rounded-lg">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                </svg>
              </div>
              <div>
                <h4 class="font-semibold text-yellow-800 mb-1">温馨提示</h4>
                <p class="text-sm text-yellow-700">
                  积分可用于下载资料、兑换奖励等。请合理获取和使用积分，严禁刷分、作弊等行为。如有违规行为，平台将保留追究责任的权利。
                </p>
              </div>
            </div>
          </div>
        </div>
      
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'

// 页面进入时滚动到顶部
onMounted(() => {
  window.scrollTo({ top: 0, behavior: 'auto' })
})
</script> 