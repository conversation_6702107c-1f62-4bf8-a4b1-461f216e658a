import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import baseURL from './src/api/baseurl'

export default defineConfig({
  server: {
    port: 3000,
    host: true,
    proxy: {
      '/api': {
        target: baseURL,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      },
      '/auth': {
        target: baseURL,
        changeOrigin: true,
        secure: false,
        ws: true
      },
      '/documents': {
        target: baseURL,
        changeOrigin: true,
        secure: false
      },
      '/upload': {
        target: baseURL,
        changeOrigin: true,
        secure: false
      }
    }
  },
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  }
}) 