<template>
  <div class="min-h-screen bg-gray-50 px-4 flex items-center justify-center">
    <div class="w-full max-w-2xl mx-auto">
      <!-- 头部区域 -->
      <div class="text-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-2">上传资料</h1>
        <p class="text-gray-600 mb-3">分享你的资料，帮助更多同学学习成长</p>
        <div class="inline-flex items-center px-3 py-1.5 bg-green-50 text-green-700 rounded-full text-sm border border-green-200">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>上传成功可获得 <span class="font-bold">+10</span> 积分奖励</span>
        </div>
      </div>

      <!-- 主要内容卡片 -->
      <div class="bg-gray-50 rounded-2xl shadow-xl border border-gray-200 p-8">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- 文件上传区域 -->
        <div>
            <label class="block text-sm font-semibold text-gray-700 mb-3">资料文件 <span class="text-red-500">*</span></label>
          <div
              class="relative border-2 border-dashed rounded-xl p-8 bg-gradient-to-br from-green-50 to-green-100 hover:from-green-100 hover:to-green-200 transition-all duration-300 cursor-pointer group min-h-[200px] flex items-center justify-center"
              :class="{ 'border-green-500 bg-green-200 shadow-lg scale-105': isDragging, 'border-gray-300': !isDragging }"
            @dragenter.prevent="isDragging = true"
            @dragleave.prevent="isDragging = false"
            @dragover.prevent
            @drop.prevent="handleFileDrop"
            @click="triggerFileSelect"
          >
              <!-- 默认状态 -->
              <div v-if="!selectedFile" class="text-center">
                <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <DocumentIcon class="h-8 w-8 text-white" />
                </div>
                <h3 class="text-lg font-semibold text-gray-700 mb-2">点击选择文件</h3>
                <p class="text-gray-500 mb-2">或将文件拖拽到此处</p>
                <p class="text-sm text-gray-400 max-w-md">支持 PDF、DOC、DOCX、JPG、PNG 格式，文件大小不超过 20MB</p>
              </div>
              
              <!-- 已选择文件状态 -->
              <div v-else class="text-center w-full">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <DocumentIcon class="h-8 w-8 text-green-600" />
                </div>
                <h3 class="text-lg font-semibold text-gray-700 mb-2">{{ selectedFile.name }}</h3>
                <p class="text-gray-500 mb-4">{{ formatFileSize(selectedFile.size) }}</p>
                <button 
                  type="button" 
                  class="inline-flex items-center px-3 py-1.5 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-colors text-sm font-medium" 
                  @click.stop="selectedFile = null"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  移除文件
                </button>
            </div>
            
            <!-- 隐藏的文件输入 -->
            <input
              ref="fileInput"
              type="file"
              class="sr-only"
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
              @change="handleFileSelect"
            />
          </div>
        </div>

          <!-- 上传进度 -->
          <div v-if="uploadProgress > 0 && uploadProgress < 100" class="space-y-3">
            <div class="flex justify-between text-sm text-gray-600">
            <span>上传进度</span>
              <span class="font-semibold">{{ uploadProgress }}%</span>
          </div>
            <div class="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
            <div 
                class="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: uploadProgress + '%' }"
            ></div>
          </div>
          <button 
            type="button"
            @click="cancelUpload"
              class="text-sm text-red-500 hover:text-red-700 hover:underline font-medium"
          >
            取消上传
          </button>
        </div>

          <!-- 文件描述 -->
        <div>
            <label for="description" class="block text-sm font-semibold text-gray-700 mb-3">
            试卷说明 <span class="text-gray-400 font-normal">(必填)</span>
          </label>
          <textarea
            id="description"
            v-model="form.description"
              rows="4"
              class="w-full border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-green-200 focus:border-green-500 text-sm px-4 py-3 placeholder-gray-400 resize-none transition-all duration-200"
              placeholder="请详细描述文件内容，包括科目、年级、考试类型、难度等级等信息..."
            required
          ></textarea>
            <p class="mt-2 text-xs text-gray-400">建议包含：科目、年级、考试类型、难度等级等详细信息</p>
        </div>

          <!-- 提交按钮 -->
        <button
          type="submit"
            class="w-full flex items-center justify-center px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white text-base font-semibold rounded-xl shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-400 transition-all duration-200 disabled:opacity-60 disabled:cursor-not-allowed transform hover:scale-[1.02] active:scale-[0.98]"
          :disabled="isSubmitting || uploadProgress > 0"
        >
            <ArrowUpTrayIcon v-if="!isSubmitting" class="h-5 w-5 mr-2" />
            <span v-if="isSubmitting" class="flex items-center gap-2">
              <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            上传中...
          </span>
            <span v-else>立即上传资料</span>
        </button>
      </form>
      </div>
    </div>

    <!-- 免责声明弹窗 -->
    <div v-if="showDisclaimerModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-gray-50 rounded-2xl shadow-2xl p-6 w-full max-w-md mx-auto transform transition-all duration-300 scale-100">
        <div class="text-center mb-6">
          <div class="w-16 h-16 bg-yellow-50 rounded-full flex items-center justify-center mx-auto mb-4">
            <ExclamationTriangleIcon class="h-8 w-8 text-yellow-500" />
          </div>
          <h3 class="text-xl font-bold text-gray-800 mb-2">免责声明</h3>
          <p class="text-gray-600">请仔细阅读以下内容</p>
        </div>
        
        <div class="mb-6">
          <div class="bg-yellow-50 p-4 rounded-xl border border-yellow-200">
            <p class="text-sm text-yellow-700 leading-relaxed">
              请确保您上传的试卷不侵犯他人知识产权，且内容真实可靠。平台仅提供分享服务，不承担因使用本平台内容所导致的任何纠纷或损失。恶意上传虚假或侵权内容将被永久封禁。
            </p>
          </div>
        </div>
        
        <div class="flex gap-3">
          <button 
            class="flex-1 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200" 
            @click="confirmUpload"
          >
            我已阅读并同意
          </button>
          <button 
            class="flex-1 py-3 bg-gray-100 text-gray-600 font-semibold rounded-xl hover:bg-gray-200 transition-all duration-200" 
            @click="showDisclaimerModal = false"
          >
            取消
          </button>
        </div>
      </div>
    </div>

    <!-- 顶部弹窗提示 -->
    <div v-if="showMessage" :class="['fixed top-6 left-1/2 z-50 px-6 py-4 rounded-xl shadow-2xl text-white text-base font-semibold transition-all duration-300 transform -translate-x-1/2', messageType === 'success' ? 'bg-gradient-to-r from-green-500 to-green-600' : 'bg-gradient-to-r from-red-500 to-red-600']">
      <div class="flex items-center gap-2">
        <svg v-if="messageType === 'success'" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
        </svg>
        <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
        {{ message }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  DocumentIcon,
  ArrowUpTrayIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline'

const router = useRouter()
const isDragging = ref(false)
const selectedFile = ref<File | null>(null)
const isSubmitting = ref(false)
const uploadProgress = ref(0)
const cancelUploadRef = ref<(() => void) | null>(null)
const form = ref({ description: '' })
const fileInput = ref<HTMLInputElement | null>(null)
const showDisclaimerModal = ref(false)

// 顶部弹窗提示
const message = ref('')
const messageType = ref<'success'|'error'>('success')
const showMessage = ref(false)
function showTip(msg: string, type: 'success'|'error' = 'success') {
  message.value = msg
  messageType.value = type
  showMessage.value = true
  setTimeout(() => { showMessage.value = false }, 2000)
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 触发文件选择
const triggerFileSelect = () => {
  fileInput.value?.click()
}

// 文件选择处理
const handleFileSelect = (event: Event) => {
  const input = event.target as HTMLInputElement
  if (input.files && input.files[0]) {
    selectedFile.value = input.files[0]
  }
}

// 文件拖拽处理
const handleFileDrop = (event: DragEvent) => {
  isDragging.value = false
  const files = event.dataTransfer?.files
  if (files && files[0]) {
    selectedFile.value = files[0]
  }
}

// 取消上传
const cancelUpload = () => {
  if (cancelUploadRef.value) {
    cancelUploadRef.value()
    uploadProgress.value = 0
    cancelUploadRef.value = null
    showTip('已取消上传', 'error')
  }
}

// 表单提交处理
const handleSubmit = async () => {
  if (!selectedFile.value) {
    showTip('请选择要上传的文件', 'error')
    return
  }
  if (!form.value.description) {
    showTip('请填写试卷描述', 'error')
    return
  }
  
  // 显示免责声明弹窗
  showDisclaimerModal.value = true
}

// 确认上传
const confirmUpload = async () => {
  showDisclaimerModal.value = false
  
  try {
    isSubmitting.value = true
    uploadProgress.value = 0
    
    const formData = new FormData()
    formData.append('file', selectedFile.value!)
    formData.append('description', form.value.description)
    
    // 使用带进度监控的上传
    const xhr = new XMLHttpRequest()
    
    // 设置进度监听
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable) {
        const progress = Math.round((event.loaded * 100) / event.total)
        uploadProgress.value = progress
      }
    })
    
    // 设置取消功能
    cancelUploadRef.value = () => {
      xhr.abort()
    }
    
    // 发送请求
    xhr.open('POST', '/api/file/upload')
    
    // 添加认证头
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    if (userInfo && userInfo.token) {
      xhr.setRequestHeader('Authorization', `Bearer ${userInfo.token}`)
    }
    
    xhr.onload = () => {
      if (xhr.status === 200) {
        showTip('上传成功！', 'success')
        setTimeout(() => router.push('/'), 1200)
      } else {
        showTip('上传失败，请重试', 'error')
      }
      isSubmitting.value = false
      uploadProgress.value = 0
      cancelUploadRef.value = null
    }
    
    xhr.onerror = () => {
      showTip('上传失败，请检查网络连接', 'error')
      isSubmitting.value = false
      uploadProgress.value = 0
      cancelUploadRef.value = null
    }
    
    xhr.send(formData)
    
  } catch (error) {
    showTip('上传失败，请重试', 'error')
    isSubmitting.value = false
    uploadProgress.value = 0
    cancelUploadRef.value = null
  }
}
</script>

<style scoped>
.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: translateY(30px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

/* 全局样式优化 */
* {
  box-sizing: border-box;
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #10b981, #059669);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #059669, #047857);
}

/* 输入框焦点效果 */
input:focus, textarea:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* 按钮悬停效果增强 */
button:hover {
  transform: translateY(-1px);
}

/* 卡片阴影效果 */
.shadow-xl {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 渐变背景动画 */
.bg-gradient-to-br {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 文件上传区域特殊效果 */
.border-dashed {
  background-image: none;
}

/* 进度条动画 */
.bg-gradient-to-r.from-green-400.to-blue-500 {
  background-size: 200% 100%;
  animation: progressPulse 2s ease-in-out infinite;
}

@keyframes progressPulse {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* 响应式优化 */
@media (max-width: 640px) {
  .max-w-2xl {
    max-width: 100%;
    margin: 0 1rem;
  }
  
  .p-8 {
    padding: 1.5rem;
  }
  
  .text-3xl {
    font-size: 1.875rem;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .bg-white {
    background-color: #1f2937;
  }
  
  .text-gray-800 {
    color: #000000;
  }
  
  .text-gray-600 {
    color: #000000;
  }
  
  .text-gray-500 {
    color: #9ca3af;
  }
  
  .border-gray-200 {
    border-color: #374151;
  }
}
</style> 