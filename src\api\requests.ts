import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse } from 'axios'

// 创建 axios 实例
const request: AxiosInstance = axios.create({
  baseURL: '/api', 
  timeout: 10000
})

// 请求拦截器，自动加上 Authorization
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    if (userInfo && userInfo.token) {
      config.headers = config.headers || {}
      config.headers['Authorization'] = `Bearer ${userInfo.token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    return response.data
  },
  (error) => {
    if (error.response) {
      switch (error.response.status) {
        case 401:
          // 未授权，清除用户信息并重定向到登录页
          // localStorage.removeItem('userInfo')
          // window.location.href = '/'
          break
        case 403:
          // 权限不足
          console.error('Permission denied')
          break
        case 500:
          // 服务器错误
          console.error('Server error')
          break
        default:
          console.error('Request failed')
      }
    }
    return Promise.reject(error)
  }
)

// 封装请求方法
export const get = (url: string, config: any = {}) => {
  return request.get(url, config)
}

export const post = (url: string, data?: any, config: any = {}) => {
  return request.post(url, data, config)
}

export const put = (url: string, data?: any, config: any = {}) => {
  return request.put(url, data, config)
}

export const del = (url: string, config: any = {}) => {
  return request.delete(url, config)
}

// 封装文件上传请求
export function upload(url: string, file: File, onProgress?: (progress: number) => void) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (e: any) => {
      if (onProgress) {
        const progress = Math.round((e.loaded * 100) / e.total)
        onProgress(progress)
      }
    }
  })
}

// 导出请求实例，以便需要时直接使用
export default request 