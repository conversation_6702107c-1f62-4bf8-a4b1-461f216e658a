<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 py-6 px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto space-y-8">
      <!-- User Info Section -->
      <div class="relative overflow-hidden bg-gradient-to-r from-emerald-500 via-teal-500 to-cyan-500 rounded-3xl shadow-2xl p-8 text-white">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
          <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" stroke-width="0.5"/>
              </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#grid)" />
          </svg>
        </div>
        
        <!-- Floating Elements -->
        <div class="absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
        <div class="absolute bottom-4 left-4 w-16 h-16 bg-white/10 rounded-full blur-lg"></div>
        
        <!-- User Avatar -->
        <div class="relative flex flex-col items-center">
          <div class="relative group">
            <div class="absolute -inset-1 bg-gradient-to-r from-pink-300 to-purple-300 rounded-full blur opacity-75 group-hover:opacity-100 transition duration-1000 group-hover:duration-200 animate-pulse"></div>
            <img 
              :src="userInfo.stu_Avator || 'https://placehold.co/150x150/10B981/FFFFFF?text=Avatar'" 
              alt="头像" 
              class="relative w-24 h-24 sm:w-28 sm:h-28 rounded-full border-4 border-white shadow-xl object-cover transform transition-transform duration-300 hover:scale-105 hover:rotate-3"
            />
          </div>
          
          <!-- User Info -->
          <div class="mt-6 text-center space-y-2">
            <h1 class="text-2xl sm:text-3xl font-bold tracking-tight">
              {{ userInfo.stu_Nickname || userInfo.stu_Name || '未登录' }}
            </h1>
            
            <div class="space-y-1 text-white/90">
              <p class="text-sm sm:text-base">学号：{{ userInfo.stu_Id || '未知' }}</p>
              <p class="text-sm sm:text-base">部门：{{ userInfo.stu_section || '未知' }}</p>
              <p class="text-sm sm:text-base">
                角色：<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 ml-1">{{ roleDisplayText }}</span>
              </p>
            </div>
            
            <!-- Credit Display -->
            <div class="mt-4 inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full">
              <svg class="w-5 h-5 text-yellow-300 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"/>
              </svg>
              <span class="text-lg font-bold text-yellow-100">{{ currentCredit }} 积分</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Feature Grid -->
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
        <!-- My Downloads -->
        <router-link to="/my-downloads" class="group relative overflow-hidden bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 hover:scale-[1.02]">
          <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-blue-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div class="relative p-6">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center group-hover:bg-blue-200 transition-colors duration-300 group-hover:scale-110 transform">
                  <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                  </svg>
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <h3 class="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">我的下载</h3>
                <p class="text-sm text-gray-500 mt-1">查看下载历史记录</p>
              </div>
              <div class="flex-shrink-0">
                <svg class="w-5 h-5 text-gray-400 group-hover:text-blue-500 group-hover:translate-x-1 transition-all duration-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
              </div>
            </div>
          </div>
        </router-link>

        <!-- My Uploads -->
        <router-link to="/my-uploads" class="group relative overflow-hidden bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 hover:scale-[1.02]">
          <div class="absolute inset-0 bg-gradient-to-br from-green-500/5 to-green-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div class="relative p-6">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center group-hover:bg-green-200 transition-colors duration-300 group-hover:scale-110 transform">
                  <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
                  </svg>
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <h3 class="text-lg font-semibold text-gray-900 group-hover:text-green-600 transition-colors duration-300">我的上传</h3>
                <p class="text-sm text-gray-500 mt-1">管理上传的资料</p>
              </div>
              <div class="flex-shrink-0">
                <svg class="w-5 h-5 text-gray-400 group-hover:text-green-500 group-hover:translate-x-1 transition-all duration-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
              </div>
            </div>
          </div>
        </router-link>

        <!-- Admin Review (conditional) -->
        <router-link v-if="isAdmin" to="/my-review" class="group relative overflow-hidden bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 hover:scale-[1.02]">
          <div class="absolute inset-0 bg-gradient-to-br from-red-500/5 to-red-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div class="relative p-6">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center group-hover:bg-red-200 transition-colors duration-300 group-hover:scale-110 transform">
                  <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                  </svg>
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <h3 class="text-lg font-semibold text-gray-900 group-hover:text-red-600 transition-colors duration-300">我被投诉</h3>
                <p class="text-sm text-gray-500 mt-1">查看被投诉的文件</p>
              </div>
              <div class="flex-shrink-0">
                <svg class="w-5 h-5 text-gray-400 group-hover:text-red-500 group-hover:translate-x-1 transition-all duration-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
              </div>
            </div>
          </div>
        </router-link>

        <!-- Points Detail -->
        <router-link to="/points-detail" class="group relative overflow-hidden bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 hover:scale-[1.02]">
          <div class="absolute inset-0 bg-gradient-to-br from-yellow-500/5 to-yellow-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div class="relative p-6">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center group-hover:bg-yellow-200 transition-colors duration-300 group-hover:scale-110 transform">
                  <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                  </svg>
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <h3 class="text-lg font-semibold text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">积分明细</h3>
                <p class="text-sm text-gray-500 mt-1">查看积分变动记录</p>
              </div>
              <div class="flex-shrink-0">
                <svg class="w-5 h-5 text-gray-400 group-hover:text-yellow-500 group-hover:translate-x-1 transition-all duration-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
              </div>
            </div>
          </div>
        </router-link>

        <!-- Points Rules -->
        <router-link to="/points-rules" class="group relative overflow-hidden bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 hover:scale-[1.02]">
          <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-purple-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div class="relative p-6">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center group-hover:bg-purple-200 transition-colors duration-300 group-hover:scale-110 transform">
                  <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <h3 class="text-lg font-semibold text-gray-900 group-hover:text-purple-600 transition-colors duration-300">积分规则</h3>
                <p class="text-sm text-gray-500 mt-1">了解积分获取规则</p>
              </div>
              <div class="flex-shrink-0">
                <svg class="w-5 h-5 text-gray-400 group-hover:text-purple-500 group-hover:translate-x-1 transition-all duration-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
              </div>
            </div>
          </div>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { get } from '@/api/requests'

interface UserInfo {
  stu_Name: string
  stu_Id: string
  stu_section: string
  stu_Avator?: string
  stu_Nickname?: string
  role?: string
  token?: string
  credit?: number
}

const route = useRoute()
const userInfo = ref<UserInfo>({
  stu_Name: '',
  stu_Id: '',
  stu_section: '',
  stu_Avator: '',
  stu_Nickname: '',
  role: '',
  token: '',
  credit: 0
})
const currentCredit = ref(0)

// Determine if user is Admin
const isAdmin = computed(() => {
  const userInfoStr = localStorage.getItem('userInfo')
  if (userInfoStr) {
    try {
      const user = JSON.parse(userInfoStr)
      return user.role === 'Admin' || user.role === 'Auditor' || user.isAdmin === true
    } catch (e) {
      console.error('Error parsing user info from localStorage:', e);
      return false
    }
  }
  return false
})

// Load user info from localStorage
const loadUserInfo = () => {
  const storedUserInfo = localStorage.getItem('userInfo')
  if (storedUserInfo) {
    try {
      const parsedInfo = JSON.parse(storedUserInfo)
      userInfo.value = {
        ...userInfo.value,
        stu_Name: parsedInfo.stu_Name || userInfo.value.stu_Name,
        stu_Id: parsedInfo.stu_Id || userInfo.value.stu_Id,
        stu_section: parsedInfo.stu_section || userInfo.value.stu_section,
        stu_Avator: parsedInfo.stu_Avator || userInfo.value.stu_Avator,
        stu_Nickname: parsedInfo.stu_Nickname || userInfo.value.stu_Nickname,
        role: parsedInfo.role || userInfo.value.role,
        token: parsedInfo.token || userInfo.value.token,
        credit: parsedInfo.credit || userInfo.value.credit
      }
      currentCredit.value = userInfo.value.credit || 0;
    } catch (e: unknown) {
      console.error('Error parsing user info:', e)
    }
  }
}

// Watch for route changes
watch(() => route.fullPath, () => {
  loadUserInfo()
})

// On component mount, load user info and latest credit
onMounted(async () => {
  window.scrollTo({ top: 0, behavior: 'auto' })
  loadUserInfo()

  // Request latest credit
  try {
    const res = await get('/getInfo/sign')
    if (res && typeof res === 'object' && 'nowCredit' in res && typeof res.nowCredit === 'number') {
      currentCredit.value = res.nowCredit;
      if (userInfo.value) {
        userInfo.value.credit = res.nowCredit;
        localStorage.setItem('userInfo', JSON.stringify(userInfo.value));
      }
    }
  } catch (e) {
    console.warn('Failed to fetch user credit:', e);
  }
})

const roleDisplayText = computed(() => {
  if (userInfo.value.role === 'Admin') {
    return '管理员'
  } else if (userInfo.value.role === 'Auditor') {
    return '审核员'
  } else {
    return '普通用户'
  }
})
</script>

<style scoped>
/* 使用 Tailwind CSS，无需额外样式 */
</style>
